<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东云服务器整机选型测试</title>
    <style>
        body {
            display: flex;
            font-family: Arial, sans-serif;
            margin: 0;
            height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: #f4f4f4;
            padding: 20px;
            border-right: 1px solid #ccc;
        }
        .content {
            flex-grow: 1;
            padding: 0;
            margin: 0;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin: 10px 0;
            cursor: pointer;
            padding: 5px;
            transition: background-color 0.3s;
        }
        .sidebar li:hover {
            background-color: #ddd;
        }
        .sidebar li.selected {
            background-color: #ccc;
            font-weight: bold;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <ul id="fileList">
            <li onclick="loadContent('./autologs/statistics.html', this)">用例执行统计报告</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_1.BIOS定制化测试（EGS GPU）-250424145608.html', this)">1.BIOS定制化测试（EGS GPU）</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_2.BMC定制化测试-250424145613.html', this)">2.BMC定制化测试</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_3.Redfish测试-250424145620.html', this)">3.Redfish测试</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_4.稳定性测试-250424145626.html', this)">4.稳定性测试</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_5.性能测试-250424145632.html', this)">5.性能测试</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_6.FRU信息测试-250424145639.html', this)">6.FRU信息测试</li>
<li onclick="loadContent('./autologs/京东云服务器整机选型测试_7.可运维测试-250424145645.html', this)">7.可运维测试</li>
        </ul>
    </div>
    <div class="content">
        <iframe id="contentFrame" src="" alt="文件内容"></iframe>
    </div>

    <script>
        // 默认加载第一个文件
        window.onload = function() {
            const firstItem = document.querySelector('.sidebar li');
            if (firstItem) {
                const firstFile = firstItem.getAttribute('onclick').match(/'([^']+)'/)[1];
                loadContent(firstFile, firstItem);
            }
        };

        function loadContent(file, element) {
            const frame = document.getElementById('contentFrame');
            frame.src = file;

            // 清除之前的选中状态
            const items = document.querySelectorAll('.sidebar li');
            items.forEach(item => {
                item.classList.remove('selected');
            });

            // 设置当前选中状态
            element.classList.add('selected');
        }
    </script>
</body>
</html>
