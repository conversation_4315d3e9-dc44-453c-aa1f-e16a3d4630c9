
<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<title>Title</title>
</head>
<body>
<table border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse"><colgroup>
<col style="width: 105.60000000000001px"/>
<col style="width: 146.88px"/>
<col style="width: 98.88px"/>
<col style="width: 413.76px"/>
<col style="width: 269.76px"/>
<col style="width: 166.07999999999998px"/>
<col style="width: 95.03999999999999px"/>
<col style="width: 98.88px"/>
</colgroup>
<tr>
<td id="7.可运维测试!A1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">分类</td>
<td id="7.可运维测试!B1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">测试项</td>
<td id="7.可运维测试!C1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">测试描述</td>
<td id="7.可运维测试!D1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">详细测试方法(步骤)(STEP By STEP)</td>
<td id="7.可运维测试!E1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">通过标准</td>
<td id="7.可运维测试!F1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">用例标识</td>
<td id="7.可运维测试!G1" style="background-color: #FF0000;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;text-align: center;vertical-align: center">测试结果</td>
</tr>
<tr>
<td id="7.可运维测试!A2" rowspan="4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">通用信息检查</td>
<td id="7.可运维测试!B2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">CPU信息检查 </td>
<td id="7.可运维测试!C2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS及OS下CPU信息正确</td>
<td id="7.可运维测试!D2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. 开机按F2进入BIOS Setup界面，检查CPU配置信息是否正确；
2. 系统下DMI、lscpu、cat /proc/cpuinfo几项检查结果是否正确；
3. 系统下检查CPU是否睿频 : lscpu CPU MHz项达到睿频</td>
<td id="7.可运维测试!E2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS Setup界面和系统下CPU型号，核数和主频正确，
lscpu下CPU核数显示正常
Idle 情况下CPU频率可以达到睿频
</td>
<td id="7.可运维测试!F2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22221</td>
<td id="7.可运维测试!G2" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22221.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B3" style="border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">内存信息检查</td>
<td id="7.可运维测试!C3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS，OS及Web下内存信息正确</td>
<td id="7.可运维测试!D3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. dmidecode -t memory | grep 'Locator'|grep -v 'Bank Locator' 检查内存槽位信息；
2. dmidecode -t memory | grep 'Part Number' | wc -l检查内存最大支持数量
3. 开机进入BIOS Setup界面，检查Memory信息是否正确
4.  BMC Web和系统下显示的内存SN是唯一标识码(18位)</td>
<td id="7.可运维测试!E3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS Setup界面，OS下和BMC Web界面内存信息正确</td>
<td id="7.可运维测试!F3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22222</td>
<td id="7.可运维测试!G3" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22222.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">硬盘信息检查</td>
<td id="7.可运维测试!C4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. 开机进入BIOS Setup界面，检查硬盘配置信息是否正确；
2. 系统下lsblk、fdisk、nvme list等命令检查硬盘型号、容量、FW等是否正确</td>
<td id="7.可运维测试!E4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS Setup界面和系统下硬盘信息正确</td>
<td id="7.可运维测试!F4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22223</td>
<td id="7.可运维测试!G4" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22223.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B5" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">网卡识别</td>
<td id="7.可运维测试!C5" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">在BIOS及OS下正常识别网卡设备</td>
<td id="7.可运维测试!D5" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. 开机进入BIOS Setup界面，检查网卡配置信息是否正确；
2. 系统下lspci等命令检查网卡型号、PN、FW等是否正确</td>
<td id="7.可运维测试!E5" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">BIOS Setup界面和系统下网卡信息正确</td>
<td id="7.可运维测试!F5" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22224</td>
<td id="7.可运维测试!G5" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22224.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A6" rowspan="21" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">BMC</td>
<td id="7.可运维测试!B6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">MAC地址检查</td>
<td id="7.可运维测试!C6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">检查共享口和专口的MAC地址是否正常</td>
<td id="7.可运维测试!D6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看共享口和专口的MAC地址是否正常，并记录MAC地址</td>
<td id="7.可运维测试!E6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">系统下查看的MAC地址和主板丝印一致</td>
<td id="7.可运维测试!F6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22225</td>
<td id="7.可运维测试!G6" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22225.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B7" style="border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">IP通信检查</td>
<td id="7.可运维测试!C7" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">检查共享口和专口IPv4是否正常</td>
<td id="7.可运维测试!D7" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">将NCSI口 配置成ipv4 DHCP模式（DHCP服务端配置自动分配网关），然后将专用口配置成静态地址，并且配置成与NCSI口不同的网关。在这种操作下检查BMC可以正常访问</td>
<td id="7.可运维测试!E7" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">NCSI口和专口BMC都可以正常访问</td>
<td id="7.可运维测试!F7" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22226</td>
<td id="7.可运维测试!G7" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22226.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B8" rowspan="11" style="border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">BMC 常用IPMI命令测试</td>
<td id="7.可运维测试!C8" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">FRU信息检查</td>
<td id="7.可运维测试!D8" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">FRU信息检查 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; fru</bmc></td>
<td id="7.可运维测试!E8" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">结果须包含京东要的厂商，机型名称，整机SN，资产编号</td>
<td id="7.可运维测试!F8" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22227</td>
<td id="7.可运维测试!G8" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22227.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C9" rowspan="4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">BMC power control
确认bmc带外开关机命令能正常执行</td>
<td id="7.可运维测试!D9" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">远程关机 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P xxxxxxxx chassis power off</bmc></td>
<td id="7.可运维测试!E9" rowspan="4" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">确认bmc带外开关机命令能正常执行</td>
<td id="7.可运维测试!F9" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22228</td>
<td id="7.可运维测试!G9" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22228.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D10" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">远程开机 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P xxxxxxxx chassis power on</bmc></td>
<td id="7.可运维测试!F10" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22228</td>
<td id="7.可运维测试!G10" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22228.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D11" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">远程重启 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P xxxxxxxx chassis power reset</bmc></td>
<td id="7.可运维测试!F11" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22228</td>
<td id="7.可运维测试!G11" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22228.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D12" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">远程硬重启 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P xxxxxxxx chassis power cycle</bmc></td>
<td id="7.可运维测试!F12" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22228</td>
<td id="7.可运维测试!G12" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22228.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C13" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center"> 节点UID灯测试
确认服务器具备UID灯功能并按照如下进行测试记录。</td>
<td id="7.可运维测试!D13" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">点亮UID灯常亮 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt;  chassis identify force</bmc></td>
<td id="7.可运维测试!E13" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">UID灯常亮，观察20秒钟</td>
<td id="7.可运维测试!F13" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22229</td>
<td id="7.可运维测试!G13" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22229.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D14" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">关闭UID灯 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis identify O</bmc></td>
<td id="7.可运维测试!E14" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">UID灯熄灭</td>
<td id="7.可运维测试!F14" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22229</td>
<td id="7.可运维测试!G14" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22229.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D15" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">点亮UID灯时间 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis identify N (1—100任选)</bmc></td>
<td id="7.可运维测试!E15" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">UID灯亮N秒并熄灭</td>
<td id="7.可运维测试!F15" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-22229</td>
<td id="7.可运维测试!G15" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-22229.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C16" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">Boot order测试
验证下一次重启引导方式生效并只生效一次后</td>
<td id="7.可运维测试!D16" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">重启进入PXE ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis bootdev pxe 
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis power reset</bmc></bmc></td>
<td id="7.可运维测试!E16" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">确认系统在OS下，使用带外命令分别验证相关指令，确认下一次重启引导方式生效并只生效一次后，需在一分钟之内重启机器。 每项测试各重复3遍，全部测试通过即为PASS.</td>
<td id="7.可运维测试!F16" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222210</td>
<td id="7.可运维测试!G16" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222210.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D17" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">重启进入BIOS ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis bootdev bios
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis power reset</bmc></bmc></td>
<td id="7.可运维测试!F17" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222210</td>
<td id="7.可运维测试!G17" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222210.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!D18" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">重启进入HDD ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis bootdev disk
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; chassis power reset</bmc></bmc></td>
<td id="7.可运维测试!F18" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222210</td>
<td id="7.可运维测试!G18" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222210.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B19" rowspan="6" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">用户设置
（BMC账号的增加及变更可以通过相应工具进行设置（有带外工具进行BMC的用户或密码的修改，用户权限的设置，包括但不限于右侧示例））</td>
<td id="7.可运维测试!C19" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D19" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center"> 查看用户ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; user list </bmc></td>
<td id="7.可运维测试!E19" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">返回正确</td>
<td id="7.可运维测试!F19" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G19" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C20" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D20" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">增加用户名 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; user  set name 4 test</bmc></td>
<td id="7.可运维测试!E20" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看用户名增加成功</td>
<td id="7.可运维测试!F20" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G20" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C21" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D21" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">修改user ID为4的用户名 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; user set name 4 test1</bmc></td>
<td id="7.可运维测试!E21" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看用户名修改成功</td>
<td id="7.可运维测试!F21" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G21" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C22" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D22" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">修改user ID为4的用户权限 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; user priv 4 4 <channel></channel></bmc></td>
<td id="7.可运维测试!E22" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看用户权限修改成功</td>
<td id="7.可运维测试!F22" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G22" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C23" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D23" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">设置user ID为4的访问权限 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; channel setaccess  <channel> 4 callin=on ipmi=true link=on privilege=4</channel></bmc></td>
<td id="7.可运维测试!E23" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看用户权限修改成功</td>
<td id="7.可运维测试!F23" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G23" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C24" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D24" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">启用user ID为4的用户 ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; user enable 4 
使用test1登录BMC，查看是否具有root权限，并有开关机，KVM查看等权限</bmc></td>
<td id="7.可运维测试!E24" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;vertical-align: center">查看修改后的用户权限可以正常使用</td>
<td id="7.可运维测试!F24" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-1560962</td>
<td id="7.可运维测试!G24" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250327201315422760/log.html#s1-s2-s1-s1-s2-s1-s2-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B25" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">静态网络测试</td>
<td id="7.可运维测试!C25" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D25" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">1. 带外配置IPv4
2.检查Share NIC是否默认为DHCP并能获取IP正常通信 
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan print  8 ，检查是否有正常返回值，并且share NIC IP Address Source, IP Address, MAC Address等信息是否正确
3. ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan set 8 ipsrc static
4. ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan print 8 检查share NIC IP Address Source是否相应变更
5. ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan set 8 ipaddr xxx.xxx.xxx.xxx
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan set 8 netmask xxx.xxx.xxx.xxx 
ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan set 8 defgw ipaddr xxx.xxx.xxx.xxx 
6. ipmitool -I lanplus -H <bmc ip=""> -U xxxxxx -P &lt;定制密码&gt; lan print 8 检查share NIC IP Address Source是否相应变更
7. 登陆BMC WebUI,确认是否可以远程访问
8. 登陆WebUI,设置Share NIC IP
9. 重新登陆Web界面确认是否生效</bmc></bmc></bmc></bmc></bmc></bmc></bmc></td>
<td id="7.可运维测试!E25" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">
1. Share NIC IP地址以及MAC地址信息正确，默认为DHCP
2.  Share NIC IP 可以从DHCP获取，且被设置为静态IP后可正常通信，实现远程访问 3. WebUI可以成功修改share NIC IP
(JD目前对于channel没有严格定义，厂商对于ncsi通道可以复用通道1)</td>
<td id="7.可运维测试!F25" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-920853</td>
<td id="7.可运维测试!G25" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250326000001251973/log.html#s1-s2-s1-s1-s3-s1-s1-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!C26" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D26" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">提供带外配置IPv6的指令方法，并确认</td>
<td id="7.可运维测试!F26" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-716354</td>
<td id="7.可运维测试!G26" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250326004528437285/log.html#s1-s1-s1-s1-s1-s1-s1-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A27" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">单双电测试</td>
<td id="7.可运维测试!B27" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">双电模式下运行geekbench脚本，记录Index分值</td>
<td id="7.可运维测试!C27" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D27" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">1. 本地测试机上下载geekbench，
2. 解压缩geekbench，
3. 确认双电源模块都处于active-avtive状态，jd定制版要求默认是a-a模式，查看方式有两种：web bmc可查看+ipmitool 命令可查看，不同厂家命令不同。
4. 执行geekbench，本次测试双电模式下跑一次，单电模式下跑一次 。
5. 跑完后，结果在result里，cat 查看即可。
6. 同时确认在线拔掉一个电源，服务器是否出现宕机或则重启。</td>
<td id="7.可运维测试!E27" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">两次Geekbench值差异在5%以内
，电源拔插服务器未出现宕机或重启
</td>
<td id="7.可运维测试!F27" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222211</td>
<td id="7.可运维测试!G27" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222211.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B28" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">拔掉一个电源(N+N冗余的情况下，拔掉N个电源)，确认服务器未出现宕机或则重启
并运行geekbench，记录Index分值</td>
<td id="7.可运维测试!C28" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;font-weight: bold;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!F28" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222211</td>
<td id="7.可运维测试!G28" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222211.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A29" rowspan="5" style="border-bottom: none;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">硬件维护</td>
<td id="7.可运维测试!B29" style="border-bottom-style: solid;border-bottom-width: 2px;border-collapse: collapse;border-left: none;border-right: none;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">节点拖拽设计</td>
<td id="7.可运维测试!C29" style="border-bottom-style: solid;border-bottom-width: 2px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;text-align: left;vertical-align: center">节点需要有拖拽把手,便于节点插拔</td>
<td id="7.可运维测试!D29" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">节点有拖拽把手</td>
<td id="7.可运维测试!E29" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;text-align: left;vertical-align: center">推荐选项，记录实际情况即可</td>
<td id="7.可运维测试!F29" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;text-align: left;vertical-align: center">SAC-222213</td>
<td id="7.可运维测试!G29" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 13.5pt"><a href="../manual/SAC-222213.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B30" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 192.0pt;text-align: left;vertical-align: center">电源热插拔实验</td>
<td id="7.可运维测试!C30" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 192.0pt;vertical-align: center">评估电源冗余工作情况，N+N冗余电源(1+1/2+2)验证电源拔插安装是否顺畅，电源PSU指示灯是否与定义一致</td>
<td id="7.可运维测试!D30" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 192.0pt;vertical-align: center">1.电源拔插是否顺畅
2.主机standby ，两个 PSU 通电: 记录灯状态
主机Power on ，两个 PSU 通电: 记录灯状态
主机Power on，一个PSU通电，另一个PSU不插电源线 PSU 通电亮绿灯，不通电灯灭
3. 使用yes加压，稳定5分钟后，拔掉一个电源模块，检查系统运行状态是否正常，检查sel日志是否异常。可用日志分析脚本检查，检查BMC、OS是否有告警
4. Web端或ipmitool命令查看负载情况（ipmitool sdr查看两个psu的功耗是否均分）</td>
<td id="7.可运维测试!E30" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 192.0pt;text-align: left;vertical-align: center">1.当测试机使用双电源时，验证本产品的两个电源可以互为冗余，且服务器处于CPU满负载时单电源掉电/重新上电不会影响设备正常运行。
2.在PSU1和PSU2同时工作的前提下，分别拔下PSU1/2，如果服务器出现宕机/重启等非正常运行情况，则表示测试失败。
3.双电情况下通过IPMI查看两个电源是否负载均衡，（PSU功耗-PSU功耗平均值）/PSU功耗平均值不超过5%，单电情况下通过IPMI查看负载是否正常BMC日志中有上述状态的记录。
</td>
<td id="7.可运维测试!F30" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 192.0pt;text-align: left;vertical-align: center">SAC-222214</td>
<td id="7.可运维测试!G30" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 192.0pt"><a href="../manual/SAC-222214.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B31" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">电源线插拔</td>
<td id="7.可运维测试!C31" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">电源线在接触不良或是电压变化过大的情况下，高压直流电源模块有时无法识别接入的电源类型，为防止此类问题发生，需进行电源线拔插测试。</td>
<td id="7.可运维测试!D31" rowspan="3" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. 双电都确认通电情况下，保证机器在os正常运行。
2. 拔掉一路电源线，然后缓慢插入电源线，注意，插入时不能过快，观察电源状态灯。确认无异常并正常供电后，拔掉另外一路电源线，并缓慢插入
3. 重复上面步骤3次，记录测试结果。
4. 查看sel有记录上下电的过程，且次数应对应一致。</td>
<td id="7.可运维测试!E31" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">电源状态灯是否异常</td>
<td id="7.可运维测试!F31" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222215</td>
<td id="7.可运维测试!G31" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222215.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!E32" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">系统是否运行稳定</td>
<td id="7.可运维测试!F32" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222215</td>
<td id="7.可运维测试!G32" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222215.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!E33" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">sel是否有异常</td>
<td id="7.可运维测试!F33" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222215</td>
<td id="7.可运维测试!G33" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222215.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A34" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">磁盘槽位测试</td>
<td id="7.可运维测试!B34" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;text-align: center;vertical-align: center">盘序</td>
<td id="7.可运维测试!C34" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">查看盘序是否满足规格书要求</td>
<td id="7.可运维测试!D34" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;vertical-align: center">确认实际物理槽位标号</td>
<td id="7.可运维测试!E34" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 13.5pt;text-align: left;vertical-align: center">满足规格书要求</td>
<td id="7.可运维测试!F34" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 13.5pt;text-align: left;vertical-align: center">SAC-222216</td>
<td id="7.可运维测试!G34" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 13.5pt"><a href="../manual/SAC-222216.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B35" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">测试物理slot号顺序</td>
<td id="7.可运维测试!C35" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center"> </td>
<td id="7.可运维测试!D35" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">1. 磁盘信号灯点亮
磁盘分三种情况测试，
   有Raid/HBA卡：./MegaCli64 -PdLocate {[-start] | -stop} -physdrv[E0:S0,E1:S1,...] -aN|-a0,1,2|-aALL
   无RAID/HBA卡：dd if=/dev/sdx of=/dev/null 
2.数据记录 
确认实际物理槽位标号，查看实测磁盘槽位是否与厂商手册中标注的槽位一致
本测试同时验证BMC web界面硬盘槽位描述，系统下的硬盘slot或盘符描述，与机箱标签顺序是否对应一致</td>
<td id="7.可运维测试!E35" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">BMC web界面硬盘slot描述，系统下的硬盘slot序号描述，与机箱硬盘标签slot号保持一致</td>
<td id="7.可运维测试!F35" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222217</td>
<td id="7.可运维测试!G35" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222217.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A36" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">风扇</td>
<td id="7.可运维测试!B36" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">风扇转速</td>
<td id="7.可运维测试!C36" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">检查风扇是否转速过高</td>
<td id="7.可运维测试!D36" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;vertical-align: center">ipmitool sdr查看风扇在100%压力稳定状态下，风扇转速不超过最大转速的83%</td>
<td id="7.可运维测试!E36" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">稳定状态下风扇转速不超过最大转速83%</td>
<td id="7.可运维测试!F36" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222218</td>
<td id="7.可运维测试!G36" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222218.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B37" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 108.0pt;text-align: center;vertical-align: center">风扇故障冗余性及智能变速测试</td>
<td id="7.可运维测试!C37" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 108.0pt;vertical-align: center">本测试主要检查风扇冗余，BMC是否会有log记录，且保障服务器正常运行。同时验证风扇自动变速能力。</td>
<td id="7.可运维测试!D37" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 108.0pt;vertical-align: center">1.确认测试BMC版本为待测版本
2.服务器模拟一颗风扇失效，双转子风扇为其中一个转子失效。（模拟方法可以为软件，也可使用塑料棒等障碍物暂时阻止风扇旋转，或使用坏风扇），或拔出风扇。阻止风扇障碍可能会导致，风扇叶片或电机损坏，测试前和厂商确认如何进行，冗余测试选择靠近CPU的风扇进行测试。
3.系统下观察是否触发SEL日志，是否正确记录故障。
4.使用系统相同核心的yes线程，对系统加压10分钟。for  i in  {1..$cpu_num}; do yes&gt;/dev/null&amp;  ;done  。同时加压内存与硬盘
6.观察系统是否有过温情况发生。
7.解除模拟故障或障碍物，使风扇正常运转。
8.检查sel log是否正确记录故障，检查sdr是否抓取所有风扇转速，检查CPU温度不超过spec。</td>
<td id="7.可运维测试!E37" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 108.0pt;vertical-align: center">1.风扇满足N+1冗余
2. 风扇故障后，正确触发sel log
3. 风扇故障解除后，所有风扇转速恢复正常运转且bmc已识别此风扇。
4.测试过程中服务器无过温。
5.自动调速验证：从idle到满载下，风扇转速能自动调整。</td>
<td id="7.可运维测试!F37" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 108.0pt;text-align: left;vertical-align: center">SAC-222219</td>
<td id="7.可运维测试!G37" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 108.0pt"><a href="../manual/SAC-222219.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A38" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">BIOS升降级测试</td>
<td id="7.可运维测试!B38" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">FW升级测试</td>
<td id="7.可运维测试!C38" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">能通过Web、带外批量工具实现对BIOS FW进行批量更新，且更新后可以保留原先配置，刷新后需重启服务器新BIOS才能生效</td>
<td id="7.可运维测试!D38" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">1.  Copy bios FW和带外工具到另外一台机器。
2.  带外进行保留参数升级。
3.  完成后进行系统重启，BIOS setup界面检查版本是否正确。
4.  系统下检查BIOS 版本和 release时间是否正确：dmidecode -t bios | grep -E "Version|Release Date"。
5. 登陆BMC Web 界面，检查BIOS版本是否正确，ME版本是否正确。
6. 步骤1-5没有问题后，刷回原版本，以保证其他测试顺利进行。
7. 使用NCSI端口，在Web页面的FW更新功能，升级FW。重新3-6检查及恢复步骤。
8.使用dedicate端口，在Web页面的FW更新功能，升级FW。重新3-6检查及恢复步骤。</td>
<td id="7.可运维测试!E38" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">固件升级成功</td>
<td id="7.可运维测试!F38" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-222220</td>
<td id="7.可运维测试!G38" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="../manual/SAC-222220.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B39" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 19pt;text-align: center;vertical-align: center">FW降级测试</td>
<td id="7.可运维测试!D39" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 19pt;text-align: left;vertical-align: center">1.  Copy biosFW和带外工具到另外一台机器。
2.  带外进行保留参数降级。
3.  完成后系统重启，BIOS setup界面检查版本是否正确
4.  系统下检查BIOS 版本和 release时间是否正确，请提供刷新截图。dmidecode -t bios | grep -E "Version|Release Date"
5. 登陆节点原BMC ip，WebUI界面检查BIOS版本是否正确，ME版本是否正确
6. 步骤1-5没有问题后，刷回原版本，以保证其他测试顺利进行。
7. 使用NCSI端口，在Web页面的FW更新功能，降级FW。重新3-6检查及恢复步骤。
8.使用dedicate端口，在Web页面的FW更新功能，降级FW。重新3-6检查及恢复步骤。</td>
<td id="7.可运维测试!E39" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 19pt;text-align: center;vertical-align: center">
固件降级成功</td>
<td id="7.可运维测试!F39" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 19pt;text-align: left;vertical-align: center">SAC-222220</td>
<td id="7.可运维测试!G39" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 19pt"><a href="../manual/SAC-222220.html" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!A40" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">BMC FW升降级测试</td>
<td id="7.可运维测试!B40" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">FW升级测试</td>
<td id="7.可运维测试!C40" rowspan="2" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">能通过Web、带外批量工具实现对BMC FW进行批量更新，且更新后可以保留原先配置</td>
<td id="7.可运维测试!D40" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">1.  Copy BMC FW和带外工具到另外一台机器。
2.  带外进行保留参数升级。
3.  刷新完成后，检查bmc 原始的配置信息是否保留，静态ip是否被修改，服务器运行正常，没有重启现象。
4.  系统下ipmitool mc info 查看bmc更新成新的版本。
5.  登陆节点原BMC ip，Web界面检查BMC版本是否正确。
6. 步骤1-5没有问题后，刷回原版本，以保证其他测试顺利进行。
7.使用NCSI端口，在Web页面的FW更新功能，只升级主版本，不升级备用版本，重启BMC。重新3-6检查及恢复步骤。升级dual flash，重启BMC，重新3-6检查机恢复步骤。
8.使用dedicate端口，在Web页面的FW更新功能，只升级主版本，不升级备用版本，重启BMC。重新3-6检查及恢复步骤。升级dual flash，重启BMC，重新3-6检查机恢复步骤。</td>
<td id="7.可运维测试!E40" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">
固件升级成功</td>
<td id="7.可运维测试!F40" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;color: #000000;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-888375</td>
<td id="7.可运维测试!G40" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250326203115425351/log.html#s1-s1-s1-s1-s2-s1-s1-t1" style="text-decoration: none;">PASS</a></td>
</tr>
<tr>
<td id="7.可运维测试!B41" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">FW降级测试</td>
<td id="7.可运维测试!D41" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">1.  Copy BMC FW和带外工具到另外一台机器。
2.  带外进行保留参数升级。
3.  刷新完成后，检查bmc 原始的配置信息是否保留，静态ip是否被修改，服务器运行正常，没有重启现象。
4.  系统下ipmitool mc info 查看bmc更新成新的版本。
5.  登陆节点原BMC ip，Web界面检查BMC版本是否正确。
6. 步骤1-5没有问题后，刷回原版本，以保证其他测试顺利进行。
7.使用NCSI端口，在Web页面的FW更新功能，只升级主版本，不升级备用版本，重启BMC。重新3-6检查及恢复步骤。升级dual flash，重启BMC，重新3-6检查机恢复步骤。
8.使用dedicate端口，在Web页面的FW更新功能，只升级主版本，不升级备用版本，重启BMC。重新3-6检查及恢复步骤。升级dual flash，重启BMC，重新3-6检查机恢复步骤。</td>
<td id="7.可运维测试!E41" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: center;vertical-align: center">
固件降级成功</td>
<td id="7.可运维测试!F41" style="border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 10.0px;height: 12.75pt;text-align: left;vertical-align: center">SAC-888373</td>
<td id="7.可运维测试!G41" style="background-color: #00FF00;border-bottom-style: solid;border-bottom-width: 1px;border-collapse: collapse;border-left-style: solid;border-left-width: 1px;border-right-style: solid;border-right-width: 1px;border-top-style: solid;border-top-width: 1px;font-size: 12.0px;height: 12.75pt"><a href="./20250326234528390392/log.html#s1-s1-s1-s1-s2-s1-s1-t1" style="text-decoration: none;">PASS</a></td>
</tr>
</table>
</body>
</html>
