<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="-1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="Robot Framework 3.1 (Python 3.7.16 on linux)" name="Generator">
<link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKcAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAAqAAAAAAAAAAAAAAAAAAAALIAAAD/AAAA4AAAANwAAADcAAAA3AAAANwAAADcAAAA3AAAANwAAADcAAAA4AAAAP8AAACxAAAAAAAAAKYAAAD/AAAAuwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC/AAAA/wAAAKkAAAD6AAAAzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN8AAAD/AAAA+gAAAMMAAAAAAAAAAgAAAGsAAABrAAAAawAAAGsAAABrAAAAawAAAGsAAABrAAAADAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAIsAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAANEAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAAAAAAAAMgAAADIAAAAyAAAAMgAAADIAAAAyAAAAMgAAADIAAAAFAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAADwAAAB8AAAAAAAAAGAAAABcAAAAAAAAAH8AAABKAAAAAAAAAAAAAAAAAAAA2gAAAP8AAAD6AAAAwwAAAAAAAADCAAAA/wAAACkAAADqAAAA4QAAAAAAAAD7AAAA/wAAALAAAAAGAAAAAAAAANoAAAD/AAAA+gAAAMMAAAAAAAAAIwAAAP4AAAD/AAAA/wAAAGAAAAAAAAAAAAAAAMkAAAD/AAAAigAAAAAAAADaAAAA/wAAAPoAAADDAAAAAAAAAAAAAAAIAAAAcAAAABkAAAAAAAAAAAAAAAAAAAAAAAAAEgAAAAAAAAAAAAAA2gAAAP8AAAD7AAAAywAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN4AAAD/AAAAqwAAAP8AAACvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALIAAAD/AAAAsgAAAAAAAAC5AAAA/wAAAMoAAADAAAAAwAAAAMAAAADAAAAAwAAAAMAAAADAAAAAwAAAAMkAAAD/AAAAvAAAAAAAAAAAAAAAAAAAAKwAAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAArQAAAAAAAAAAwAMAAIABAAAf+AAAP/wAAD/8AAAgBAAAP/wAAD/8AAA//AAAJIwAADHEAAA//AAAP/wAAB/4AACAAQAAwAMAAA==">
<style media="all" type="text/css">
/* Generic and misc styles */
body {
    font-family: Helvetica, sans-serif;
    font-size: 0.8em;
    color: black;
    padding: 6px;
    background: white;
}
table {
    table-layout: fixed;
    word-wrap: break-word;
    empty-cells: show;
    font-size: 1em;
}
th, td {
    vertical-align: top;
}
br {
    mso-data-placement: same-cell; /* maintain line breaks in Excel */
}
hr {
    background: #ccc;
    height: 1px;
    border: 0;
}
a, a:link, a:visited {
    text-decoration: none;
    color: #15c;
}
a > img {
    border: 1px solid #15c !important;
}
a:hover, a:active {
    text-decoration: underline;
    color: #61c;
}
.parent-name {
    font-size: 0.7em;
    letter-spacing: -0.07em;
}
.message {
    white-space: pre-wrap;
}
/* Headers */
#header {
    width: 65em;
    height: 3em;
    margin: 6px 0;
}
h1 {
    float: left;
    margin: 0 0 0.5em 0;
    width: 75%;
}
h2 {
    clear: left;
}
#generated {
    float: right;
    text-align: right;
    font-size: 0.9em;
    white-space: nowrap;
}
/* Documentation headers */
.doc > h2 {
    font-size: 1.2em;
}
.doc > h3 {
    font-size: 1.1em;
}
.doc > h4 {
    font-size: 1.0em;
}
/* Status text colors -- !important allows using them in links */
.fail {
    color: #f33 !important;
    font-weight: bold;
}
.pass {
    color: #393 !important;
}
.label {
    padding: 2px 5px;
    font-size: 0.75em;
    letter-spacing: 1px;
    white-space: nowrap;
    color: black;
    background-color: #ddd;
    border-radius: 3px;
}
.label.debug, .label.trace, .label.error, .label.keyword {
    letter-spacing: 0;
}
.label.error, .label.fail, .label.pass, .label.warn {
    color: #fff !important;
    font-weight: bold;
}
.label.error, .label.fail {
    background-color: #d9534f;
}
.label.pass {
    background-color: #5cb85c;
}
.label.warn {
    background-color: #ec971f;
}
/* Top right header */
#top-right-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    width: 12em;
    text-align: center;
}
#report-or-log-link a {
    display: block;
    background: black;
    color: white;
    text-decoration: none;
    font-weight: bold;
    letter-spacing: 0.1em;
    padding: 0.3em 0;
    border-bottom-left-radius: 4px;
}
#report-or-log-link a:hover {
    color: #ddd;
}
#log-level-selector {
    padding: 0.3em 0;
    font-size: 0.9em;
    border-bottom-left-radius: 4px;
    background: #ddd;
}
/* Statistics table */
.statistics {
    width: 65em;
    border-collapse: collapse;
    empty-cells: show;
    margin-bottom: 1em;
}
.statistics tr:hover {
    background: #f4f4f4;
    cursor: pointer;
}
.statistics th, .statistics td {
    border: 1px solid #ccc;
    padding: 0.1em 0.3em;
}
.statistics th {
    background-color: #ddd;
    padding: 0.2em 0.3em;
}
.statistics td {
    vertical-align: middle;
}
.stats-col-stat {
    width: 4.5em;
    text-align: center;
}
.stats-col-elapsed {
    width: 5.5em;
    text-align: center;
}
.stats-col-graph {
    width: 9em;
}
th.stats-col-graph:hover {
    cursor: default;
}
.stat-name {
    float: left;
}
.stat-name a, .stat-name span {
    font-weight: bold;
}
.tag-links {
    font-size: 0.9em;
    float: right;
    margin-top: 0.05em;
}
.tag-links span {
    margin-left: 0.2em;
}
/* Statistics graph */
.graph, .empty-graph {
    border: 1px solid #ccc;
    width: auto;
    height: 7px;
    padding: 0;
    background: #f33;
}
.empty-graph {
    background: #eee;
}
.pass-bar, .fail-bar {
    float: left;
    height: 100%;
}
.pass-bar {
    background: #1d4;
}
/* Tablesorter - adapted from provided Blue Skin */
.tablesorter-header {
    background-image: url(data:image/gif;base64,R0lGODlhCwAJAIAAAH9/fwAAACH5BAEAAAEALAAAAAALAAkAAAIRjAOnBr3cnIr0WUjTrC9e9BQAOw==);
    background-repeat: no-repeat;
    background-position: center right;
    cursor: pointer;
}
.tablesorter-header:hover {
    background-color: #ccc;
}
.tablesorter-headerAsc {
    background-image: url(data:image/gif;base64,R0lGODlhCwAJAKEAAAAAAH9/fwAAAAAAACH5BAEAAAIALAAAAAALAAkAAAIUlBWnFr3cnIr0WQOyBmvzp13CpxQAOw==);
    background-color: #ccc !important;
}
.tablesorter-headerDesc {
    background-image: url(data:image/gif;base64,R0lGODlhCwAJAKEAAAAAAH9/fwAAAAAAACH5BAEAAAIALAAAAAALAAkAAAIUlAWnBr3cnIr0WROyDmvzp13CpxQAOw==);
    background-color: #ccc !important;
}
.sorter-false {
    background-image: none;
    cursor: default;
}
.sorter-false:hover {
    background-color: #ddd;
}
</style>
<style media="all" type="text/css">
/* Generic table styles */
table {
    margin: 0 1px;
    background: white;
}
tr {
    background: white;
}
th {
    background: #ddd;
    color: black;
}
/* Summary and total/tag/suite details */
.details {
    border: 1px solid #ccc;
    border-collapse: collapse;
    clear: both;
    width: 65em;
    margin-bottom: 1em;
}
.details th {
    background: white;
    width: 10em;
    white-space: nowrap;
    text-align: left;
    vertical-align: top;
    padding: 0.2em 0.4em;
}
.details td {
    vertical-align: top;
    padding: 0.2em 0.4em;
}
.selector th, .selector td {
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    white-space: normal;
}
.first-selector th, .first-selector td{
    padding-bottom: 0.2em;
}
.middle-selector th, .middle-selector td {
    padding-top: 0.2em;
    padding-bottom: 0.2em;
}
.last-selector th, .last-selector td{
    padding-top: 0.2em;
}
#print-selector {
    display: none;
}
/* Search */
#search-suite, #search-test, #search-include, #search-exclude {
    width: 25em;
}
#search a:hover {
    text-decoration: none;
}
#search-help div {
    margin: 0.5em 0.5em 0.7em 0;
    padding: 0.7em;
    background: #eee;
    border: 1px solid #ccc;
    border-radius: 3px;
}
#search-help h3, #search-help p {
    margin: 0 0 0.7em 0;
}
.search-help-examples {
    table-layout: auto;
    width: 100%;
}
.search-help-examples, .search-help-examples tr,
.search-help-examples th, .search-help-examples td {
    background: transparent;
    margin: 0;
}
.search-help-examples th, .search-help-examples td {
    padding: 0.2em 0.7em 0.2em 0;
}
.help-item {
    width: 10%;
}
.help-explanation {
    width: 65%;
}
.help-examples {
    width: 25%;
}
/* Tabs - adapted from http://www.htmldog.com/articles/tabs */
#detail-tabs {
    list-style: none;
    padding: 0;
    margin: 0 1em;
}
.detail-tab {
    float: left;
    background: #ddd;
    border: 1px solid #ccc;
    border-bottom-width: 0;
    margin: 0 0.5em 0 0;
    padding-top: 0.1em;
    border-radius: 3px 3px 0 0;
}
.detail-tab:hover {
    background: #ccc;
}
.detail-tab a {
    color: black;
    text-decoration: none;
    font-weight: bold;
    padding: 0 1em;
}
.detail-tab-selected {
    position: relative;
    top: 1px;
    background: white;
}
.detail-tab-selected:hover {
    background: white;
}
/* Test details table */
#test-details {
    width: 100%;
    border-spacing: 1px;
    background-color: #ccc;
}
#test-details > tbody > tr:hover {
    background: #f4f4f4;
    cursor: pointer;
}
#test-details th, #test-details td {
    padding: 0.2em;
}
.details-limited {
    max-height: 20em;
    overflow: auto;
}
.details-col-header {
    padding-right: 1em;
}
.details-col-toggle {
    float: left;
    color: #6c6c6c; /* same as in sort icon */
    cursor: pointer;
}
.details-col-toggle:hover {
    color: black;
}
.details-col-name {
    min-width: 20em;
    font-weight: bold;
}
.details-col-doc {
    min-width: 10em;
}
.details-col-tags {
    min-width: 10em;
}
.details-col-crit {
    width: 3.5em;
    text-align: center;
}
.details-col-status {
    width: 4.5em;
    text-align: center;
}
.details-col-msg {
    min-width: 20em;
}
.details-col-elapsed {
    width: 7em;
    text-align: center;
}
.details-col-times {
    width: 11em;
    white-space: nowrap;
    text-align: center;
}
.hidden .details-col-header, td.hidden > div {
    display: none;
}
.hidden {
    width: 13px;
    min-width: 0;
    background-image: none;
}
th.hidden:hover {
    background-color: #ccc;
}
</style>
<style media="print" type="text/css">
body {
    background: white !important;
    padding: 0;
    font-size: 8pt;
}
a:link, a:visited {
    color: black;
}
#header {
    width: auto;
}
.details, .statistics {
    width: 100%;
}
#generated-ago, #top-right-header, #normal-selector, #search-buttons,
.folding-button, .expand, .hidden, .details-col-toggle {
    display: none;
}
.element-header-text, .children {
    margin: 0;
}
#test-details {
    border-collapse: collapse;
    table-layout: auto;
}
#test-details th, #test-details td {
    border: 1px solid black;
}
.details-col-header {
    padding: 0;
}
#print-selector {
    display: table-cell;
}
.tablesorter-header {
    background-image: none;
    background: #ddd !important;
}
</style>
<style media="all" type="text/css">
#javascript-disabled {
    width: 600px;
    margin: 100px auto 0 auto;
    padding: 20px;
    color: black;
    border: 1px solid #ccc;
    background: #eee;
}
#javascript-disabled h1 {
    width: 100%;
    float: none;
}
#javascript-disabled ul {
    font-size: 1.2em;
}
#javascript-disabled li {
    margin: 0.5em 0;
}
#javascript-disabled b {
    font-style: italic;
}
</style>
<style media="all" type="text/css">
.doc > * {
    margin: 0.7em 1em 0.1em 1em;
    padding: 0;
}
.doc > p, .doc > h1, .doc > h2, .doc > h3, .doc > h4 {
    margin: 0.7em 0 0.1em 0;
}
.doc > *:first-child {
    margin-top: 0.1em;
}
.doc table {
    border: 1px solid #ccc;
    background: transparent;
    border-collapse: collapse;
    empty-cells: show;
    font-size: 0.9em;
}
.doc table th, .doc table td {
    border: 1px solid #ccc;
    background: transparent;
    padding: 0.1em 0.3em;
    height: 1.2em;
}
.doc table th {
    text-align: center;
    letter-spacing: 0.1em;
}
.doc pre {
    font-size: 1.1em;
    letter-spacing: 0.05em;
    background: #f4f4f4;
}
.doc code {
    padding: 0 0.2em;
    letter-spacing: 0.05em;
    background: #eee;
}
.doc li {
    list-style-position: inside;
    list-style-type: square;
}
.doc img {
    border: 1px solid #ccc;
}
.doc hr {
    background: #ccc;
    height: 1px;
    border: 0;
}
</style>
<script type="text/javascript">
/*! jQuery v3.3.1 | (c) JS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";var n=[],r=e.document,i=Object.getPrototypeOf,o=n.slice,a=n.concat,s=n.push,u=n.indexOf,l={},c=l.toString,f=l.hasOwnProperty,p=f.toString,d=p.call(Object),h={},g=function e(t){return"function"==typeof t&&"number"!=typeof t.nodeType},y=function e(t){return null!=t&&t===t.window},v={type:!0,src:!0,noModule:!0};function m(e,t,n){var i,o=(t=t||r).createElement("script");if(o.text=e,n)for(i in v)n[i]&&(o[i]=n[i]);t.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[c.call(e)]||"object":typeof e}var b="3.3.1",w=function(e,t){return new w.fn.init(e,t)},T=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;w.fn=w.prototype={jquery:"3.3.1",constructor:w,length:0,toArray:function(){return o.call(this)},get:function(e){return null==e?o.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=w.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return w.each(this,e)},map:function(e){return this.pushStack(w.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:n.sort,splice:n.splice},w.extend=w.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||g(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)n=a[t],a!==(r=e[t])&&(l&&r&&(w.isPlainObject(r)||(i=Array.isArray(r)))?(i?(i=!1,o=n&&Array.isArray(n)?n:[]):o=n&&w.isPlainObject(n)?n:{},a[t]=w.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},w.extend({expando:"jQuery"+("3.3.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==c.call(e))&&(!(t=i(e))||"function"==typeof(n=f.call(t,"constructor")&&t.constructor)&&p.call(n)===d)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e){m(e)},each:function(e,t){var n,r=0;if(C(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(T,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(C(Object(e))?w.merge(n,"string"==typeof e?[e]:e):s.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;o<a;o++)(r=!t(e[o],o))!==s&&i.push(e[o]);return i},map:function(e,t,n){var r,i,o=0,s=[];if(C(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&s.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&s.push(i);return a.apply([],s)},guid:1,support:h}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=n[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()});function C(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!g(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}var E=function(e){var t,n,r,i,o,a,s,u,l,c,f,p,d,h,g,y,v,m,x,b="sizzle"+1*new Date,w=e.document,T=0,C=0,E=ae(),k=ae(),S=ae(),D=function(e,t){return e===t&&(f=!0),0},N={}.hasOwnProperty,A=[],j=A.pop,q=A.push,L=A.push,H=A.slice,O=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},P="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",R="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",I="\\["+M+"*("+R+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+R+"))|)"+M+"*\\]",W=":("+R+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+I+")*)|.*)\\)|)",$=new RegExp(M+"+","g"),B=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g"),F=new RegExp("^"+M+"*,"+M+"*"),_=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),z=new RegExp("="+M+"*([^\\]'\"]*?)"+M+"*\\]","g"),X=new RegExp(W),U=new RegExp("^"+R+"$"),V={ID:new RegExp("^#("+R+")"),CLASS:new RegExp("^\\.("+R+")"),TAG:new RegExp("^("+R+"|[*])"),ATTR:new RegExp("^"+I),PSEUDO:new RegExp("^"+W),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+P+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},G=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,Q=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,K=/[+~]/,Z=new RegExp("\\\\([\\da-f]{1,6}"+M+"?|("+M+")|.)","ig"),ee=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},te=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ne=function(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},re=function(){p()},ie=me(function(e){return!0===e.disabled&&("form"in e||"label"in e)},{dir:"parentNode",next:"legend"});try{L.apply(A=H.call(w.childNodes),w.childNodes),A[w.childNodes.length].nodeType}catch(e){L={apply:A.length?function(e,t){q.apply(e,H.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}function oe(e,t,r,i){var o,s,l,c,f,h,v,m=t&&t.ownerDocument,T=t?t.nodeType:9;if(r=r||[],"string"!=typeof e||!e||1!==T&&9!==T&&11!==T)return r;if(!i&&((t?t.ownerDocument||t:w)!==d&&p(t),t=t||d,g)){if(11!==T&&(f=J.exec(e)))if(o=f[1]){if(9===T){if(!(l=t.getElementById(o)))return r;if(l.id===o)return r.push(l),r}else if(m&&(l=m.getElementById(o))&&x(t,l)&&l.id===o)return r.push(l),r}else{if(f[2])return L.apply(r,t.getElementsByTagName(e)),r;if((o=f[3])&&n.getElementsByClassName&&t.getElementsByClassName)return L.apply(r,t.getElementsByClassName(o)),r}if(n.qsa&&!S[e+" "]&&(!y||!y.test(e))){if(1!==T)m=t,v=e;else if("object"!==t.nodeName.toLowerCase()){(c=t.getAttribute("id"))?c=c.replace(te,ne):t.setAttribute("id",c=b),s=(h=a(e)).length;while(s--)h[s]="#"+c+" "+ve(h[s]);v=h.join(","),m=K.test(e)&&ge(t.parentNode)||t}if(v)try{return L.apply(r,m.querySelectorAll(v)),r}catch(e){}finally{c===b&&t.removeAttribute("id")}}}return u(e.replace(B,"$1"),t,r,i)}function ae(){var e=[];function t(n,i){return e.push(n+" ")>r.cacheLength&&delete t[e.shift()],t[n+" "]=i}return t}function se(e){return e[b]=!0,e}function ue(e){var t=d.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function le(e,t){var n=e.split("|"),i=n.length;while(i--)r.attrHandle[n[i]]=t}function ce(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function fe(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function de(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&ie(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function he(e){return se(function(t){return t=+t,se(function(n,r){var i,o=e([],n.length,t),a=o.length;while(a--)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function ge(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}n=oe.support={},o=oe.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},p=oe.setDocument=function(e){var t,i,a=e?e.ownerDocument||e:w;return a!==d&&9===a.nodeType&&a.documentElement?(d=a,h=d.documentElement,g=!o(d),w!==d&&(i=d.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",re,!1):i.attachEvent&&i.attachEvent("onunload",re)),n.attributes=ue(function(e){return e.className="i",!e.getAttribute("className")}),n.getElementsByTagName=ue(function(e){return e.appendChild(d.createComment("")),!e.getElementsByTagName("*").length}),n.getElementsByClassName=Q.test(d.getElementsByClassName),n.getById=ue(function(e){return h.appendChild(e).id=b,!d.getElementsByName||!d.getElementsByName(b).length}),n.getById?(r.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){return e.getAttribute("id")===t}},r.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}}):(r.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},r.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&g){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),r.find.TAG=n.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&r.push(n);return r}return o},r.find.CLASS=n.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&g)return t.getElementsByClassName(e)},v=[],y=[],(n.qsa=Q.test(d.querySelectorAll))&&(ue(function(e){h.appendChild(e).innerHTML="<a id='"+b+"'></a><select id='"+b+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&y.push("[*^$]="+M+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||y.push("\\["+M+"*(?:value|"+P+")"),e.querySelectorAll("[id~="+b+"-]").length||y.push("~="),e.querySelectorAll(":checked").length||y.push(":checked"),e.querySelectorAll("a#"+b+"+*").length||y.push(".#.+[+~]")}),ue(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=d.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&y.push("name"+M+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&y.push(":enabled",":disabled"),h.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&y.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),y.push(",.*:")})),(n.matchesSelector=Q.test(m=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&ue(function(e){n.disconnectedMatch=m.call(e,"*"),m.call(e,"[s!='']:x"),v.push("!=",W)}),y=y.length&&new RegExp(y.join("|")),v=v.length&&new RegExp(v.join("|")),t=Q.test(h.compareDocumentPosition),x=t||Q.test(h.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},D=t?function(e,t){if(e===t)return f=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(1&(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===r?e===d||e.ownerDocument===w&&x(w,e)?-1:t===d||t.ownerDocument===w&&x(w,t)?1:c?O(c,e)-O(c,t):0:4&r?-1:1)}:function(e,t){if(e===t)return f=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e===d?-1:t===d?1:i?-1:o?1:c?O(c,e)-O(c,t):0;if(i===o)return ce(e,t);n=e;while(n=n.parentNode)a.unshift(n);n=t;while(n=n.parentNode)s.unshift(n);while(a[r]===s[r])r++;return r?ce(a[r],s[r]):a[r]===w?-1:s[r]===w?1:0},d):d},oe.matches=function(e,t){return oe(e,null,null,t)},oe.matchesSelector=function(e,t){if((e.ownerDocument||e)!==d&&p(e),t=t.replace(z,"='$1']"),n.matchesSelector&&g&&!S[t+" "]&&(!v||!v.test(t))&&(!y||!y.test(t)))try{var r=m.call(e,t);if(r||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(e){}return oe(t,d,null,[e]).length>0},oe.contains=function(e,t){return(e.ownerDocument||e)!==d&&p(e),x(e,t)},oe.attr=function(e,t){(e.ownerDocument||e)!==d&&p(e);var i=r.attrHandle[t.toLowerCase()],o=i&&N.call(r.attrHandle,t.toLowerCase())?i(e,t,!g):void 0;return void 0!==o?o:n.attributes||!g?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},oe.escape=function(e){return(e+"").replace(te,ne)},oe.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},oe.uniqueSort=function(e){var t,r=[],i=0,o=0;if(f=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(D),f){while(t=e[o++])t===e[o]&&(i=r.push(o));while(i--)e.splice(r[i],1)}return c=null,e},i=oe.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else while(t=e[r++])n+=i(t);return n},(r=oe.selectors={cacheLength:50,createPseudo:se,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Z,ee),e[3]=(e[3]||e[4]||e[5]||"").replace(Z,ee),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||oe.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&oe.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return V.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=a(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Z,ee).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=E[e+" "];return t||(t=new RegExp("(^|"+M+")"+e+"("+M+"|$)"))&&E(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var i=oe.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace($," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h,g=o!==a?"nextSibling":"previousSibling",y=t.parentNode,v=s&&t.nodeName.toLowerCase(),m=!u&&!s,x=!1;if(y){if(o){while(g){p=t;while(p=p[g])if(s?p.nodeName.toLowerCase()===v:1===p.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?y.firstChild:y.lastChild],a&&m){x=(d=(l=(c=(f=(p=y)[b]||(p[b]={}))[p.uniqueID]||(f[p.uniqueID]={}))[e]||[])[0]===T&&l[1])&&l[2],p=d&&y.childNodes[d];while(p=++d&&p&&p[g]||(x=d=0)||h.pop())if(1===p.nodeType&&++x&&p===t){c[e]=[T,d,x];break}}else if(m&&(x=d=(l=(c=(f=(p=t)[b]||(p[b]={}))[p.uniqueID]||(f[p.uniqueID]={}))[e]||[])[0]===T&&l[1]),!1===x)while(p=++d&&p&&p[g]||(x=d=0)||h.pop())if((s?p.nodeName.toLowerCase()===v:1===p.nodeType)&&++x&&(m&&((c=(f=p[b]||(p[b]={}))[p.uniqueID]||(f[p.uniqueID]={}))[e]=[T,x]),p===t))break;return(x-=i)===r||x%r==0&&x/r>=0}}},PSEUDO:function(e,t){var n,i=r.pseudos[e]||r.setFilters[e.toLowerCase()]||oe.error("unsupported pseudo: "+e);return i[b]?i(t):i.length>1?(n=[e,e,"",t],r.setFilters.hasOwnProperty(e.toLowerCase())?se(function(e,n){var r,o=i(e,t),a=o.length;while(a--)e[r=O(e,o[a])]=!(n[r]=o[a])}):function(e){return i(e,0,n)}):i}},pseudos:{not:se(function(e){var t=[],n=[],r=s(e.replace(B,"$1"));return r[b]?se(function(e,t,n,i){var o,a=r(e,null,i,[]),s=e.length;while(s--)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:se(function(e){return function(t){return oe(e,t).length>0}}),contains:se(function(e){return e=e.replace(Z,ee),function(t){return(t.textContent||t.innerText||i(t)).indexOf(e)>-1}}),lang:se(function(e){return U.test(e||"")||oe.error("unsupported lang: "+e),e=e.replace(Z,ee).toLowerCase(),function(t){var n;do{if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:de(!1),disabled:de(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!r.pseudos.empty(e)},header:function(e){return Y.test(e.nodeName)},input:function(e){return G.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:he(function(){return[0]}),last:he(function(e,t){return[t-1]}),eq:he(function(e,t,n){return[n<0?n+t:n]}),even:he(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:he(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:he(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:he(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=r.pseudos.eq;for(t in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[t]=fe(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=pe(t);function ye(){}ye.prototype=r.filters=r.pseudos,r.setFilters=new ye,a=oe.tokenize=function(e,t){var n,i,o,a,s,u,l,c=k[e+" "];if(c)return t?0:c.slice(0);s=e,u=[],l=r.preFilter;while(s){n&&!(i=F.exec(s))||(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),n=!1,(i=_.exec(s))&&(n=i.shift(),o.push({value:n,type:i[0].replace(B," ")}),s=s.slice(n.length));for(a in r.filter)!(i=V[a].exec(s))||l[a]&&!(i=l[a](i))||(n=i.shift(),o.push({value:n,type:a,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?oe.error(e):k(e,u).slice(0)};function ve(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function me(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=C++;return t.first?function(t,n,i){while(t=t[r])if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,u){var l,c,f,p=[T,s];if(u){while(t=t[r])if((1===t.nodeType||a)&&e(t,n,u))return!0}else while(t=t[r])if(1===t.nodeType||a)if(f=t[b]||(t[b]={}),c=f[t.uniqueID]||(f[t.uniqueID]={}),i&&i===t.nodeName.toLowerCase())t=t[r]||t;else{if((l=c[o])&&l[0]===T&&l[1]===s)return p[2]=l[2];if(c[o]=p,p[2]=e(t,n,u))return!0}return!1}}function xe(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function be(e,t,n){for(var r=0,i=t.length;r<i;r++)oe(e,t[r],n);return n}function we(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function Te(e,t,n,r,i,o){return r&&!r[b]&&(r=Te(r)),i&&!i[b]&&(i=Te(i,o)),se(function(o,a,s,u){var l,c,f,p=[],d=[],h=a.length,g=o||be(t||"*",s.nodeType?[s]:s,[]),y=!e||!o&&t?g:we(g,p,e,s,u),v=n?i||(o?e:h||r)?[]:a:y;if(n&&n(y,v,s,u),r){l=we(v,d),r(l,[],s,u),c=l.length;while(c--)(f=l[c])&&(v[d[c]]=!(y[d[c]]=f))}if(o){if(i||e){if(i){l=[],c=v.length;while(c--)(f=v[c])&&l.push(y[c]=f);i(null,v=[],l,u)}c=v.length;while(c--)(f=v[c])&&(l=i?O(o,f):p[c])>-1&&(o[l]=!(a[l]=f))}}else v=we(v===a?v.splice(h,v.length):v),i?i(null,a,v,u):L.apply(a,v)})}function Ce(e){for(var t,n,i,o=e.length,a=r.relative[e[0].type],s=a||r.relative[" "],u=a?1:0,c=me(function(e){return e===t},s,!0),f=me(function(e){return O(t,e)>-1},s,!0),p=[function(e,n,r){var i=!a&&(r||n!==l)||((t=n).nodeType?c(e,n,r):f(e,n,r));return t=null,i}];u<o;u++)if(n=r.relative[e[u].type])p=[me(xe(p),n)];else{if((n=r.filter[e[u].type].apply(null,e[u].matches))[b]){for(i=++u;i<o;i++)if(r.relative[e[i].type])break;return Te(u>1&&xe(p),u>1&&ve(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(B,"$1"),n,u<i&&Ce(e.slice(u,i)),i<o&&Ce(e=e.slice(i)),i<o&&ve(e))}p.push(n)}return xe(p)}function Ee(e,t){var n=t.length>0,i=e.length>0,o=function(o,a,s,u,c){var f,h,y,v=0,m="0",x=o&&[],b=[],w=l,C=o||i&&r.find.TAG("*",c),E=T+=null==w?1:Math.random()||.1,k=C.length;for(c&&(l=a===d||a||c);m!==k&&null!=(f=C[m]);m++){if(i&&f){h=0,a||f.ownerDocument===d||(p(f),s=!g);while(y=e[h++])if(y(f,a||d,s)){u.push(f);break}c&&(T=E)}n&&((f=!y&&f)&&v--,o&&x.push(f))}if(v+=m,n&&m!==v){h=0;while(y=t[h++])y(x,b,a,s);if(o){if(v>0)while(m--)x[m]||b[m]||(b[m]=j.call(u));b=we(b)}L.apply(u,b),c&&!o&&b.length>0&&v+t.length>1&&oe.uniqueSort(u)}return c&&(T=E,l=w),x};return n?se(o):o}return s=oe.compile=function(e,t){var n,r=[],i=[],o=S[e+" "];if(!o){t||(t=a(e)),n=t.length;while(n--)(o=Ce(t[n]))[b]?r.push(o):i.push(o);(o=S(e,Ee(i,r))).selector=e}return o},u=oe.select=function(e,t,n,i){var o,u,l,c,f,p="function"==typeof e&&e,d=!i&&a(e=p.selector||e);if(n=n||[],1===d.length){if((u=d[0]=d[0].slice(0)).length>2&&"ID"===(l=u[0]).type&&9===t.nodeType&&g&&r.relative[u[1].type]){if(!(t=(r.find.ID(l.matches[0].replace(Z,ee),t)||[])[0]))return n;p&&(t=t.parentNode),e=e.slice(u.shift().value.length)}o=V.needsContext.test(e)?0:u.length;while(o--){if(l=u[o],r.relative[c=l.type])break;if((f=r.find[c])&&(i=f(l.matches[0].replace(Z,ee),K.test(u[0].type)&&ge(t.parentNode)||t))){if(u.splice(o,1),!(e=i.length&&ve(u)))return L.apply(n,i),n;break}}}return(p||s(e,d))(i,t,!g,n,!t||K.test(e)&&ge(t.parentNode)||t),n},n.sortStable=b.split("").sort(D).join("")===b,n.detectDuplicates=!!f,p(),n.sortDetached=ue(function(e){return 1&e.compareDocumentPosition(d.createElement("fieldset"))}),ue(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||le("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),n.attributes&&ue(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||le("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ue(function(e){return null==e.getAttribute("disabled")})||le(P,function(e,t,n){var r;if(!n)return!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),oe}(e);w.find=E,w.expr=E.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=E.uniqueSort,w.text=E.getText,w.isXMLDoc=E.isXML,w.contains=E.contains,w.escapeSelector=E.escape;var k=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&w(e).is(n))break;r.push(e)}return r},S=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},D=w.expr.match.needsContext;function N(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var A=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function j(e,t,n){return g(t)?w.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?w.grep(e,function(e){return e===t!==n}):"string"!=typeof t?w.grep(e,function(e){return u.call(t,e)>-1!==n}):w.filter(t,e,n)}w.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?w.find.matchesSelector(r,e)?[r]:[]:w.find.matches(e,w.grep(t,function(e){return 1===e.nodeType}))},w.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(w(e).filter(function(){for(t=0;t<r;t++)if(w.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)w.find(e,i[t],n);return r>1?w.uniqueSort(n):n},filter:function(e){return this.pushStack(j(this,e||[],!1))},not:function(e){return this.pushStack(j(this,e||[],!0))},is:function(e){return!!j(this,"string"==typeof e&&D.test(e)?w(e):e||[],!1).length}});var q,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(e,t,n){var i,o;if(!e)return this;if(n=n||q,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:L.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof w?t[0]:t,w.merge(this,w.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:r,!0)),A.test(i[1])&&w.isPlainObject(t))for(i in t)g(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(o=r.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):g(e)?void 0!==n.ready?n.ready(e):e(w):w.makeArray(e,this)}).prototype=w.fn,q=w(r);var H=/^(?:parents|prev(?:Until|All))/,O={children:!0,contents:!0,next:!0,prev:!0};w.fn.extend({has:function(e){var t=w(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(w.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&w(e);if(!D.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&w.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?w.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(w(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}});function P(e,t){while((e=e[t])&&1!==e.nodeType);return e}w.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return k(e,"parentNode")},parentsUntil:function(e,t,n){return k(e,"parentNode",n)},next:function(e){return P(e,"nextSibling")},prev:function(e){return P(e,"previousSibling")},nextAll:function(e){return k(e,"nextSibling")},prevAll:function(e){return k(e,"previousSibling")},nextUntil:function(e,t,n){return k(e,"nextSibling",n)},prevUntil:function(e,t,n){return k(e,"previousSibling",n)},siblings:function(e){return S((e.parentNode||{}).firstChild,e)},children:function(e){return S(e.firstChild)},contents:function(e){return N(e,"iframe")?e.contentDocument:(N(e,"template")&&(e=e.content||e),w.merge([],e.childNodes))}},function(e,t){w.fn[e]=function(n,r){var i=w.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=w.filter(r,i)),this.length>1&&(O[e]||w.uniqueSort(i),H.test(e)&&i.reverse()),this.pushStack(i)}});var M=/[^\x20\t\r\n\f]+/g;function R(e){var t={};return w.each(e.match(M)||[],function(e,n){t[n]=!0}),t}w.Callbacks=function(e){e="string"==typeof e?R(e):w.extend({},e);var t,n,r,i,o=[],a=[],s=-1,u=function(){for(i=i||e.once,r=t=!0;a.length;s=-1){n=a.shift();while(++s<o.length)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1)}e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){w.each(n,function(n,r){g(r)?e.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==x(r)&&t(r)})}(arguments),n&&!t&&u()),this},remove:function(){return w.each(arguments,function(e,t){var n;while((n=w.inArray(t,o,n))>-1)o.splice(n,1),n<=s&&s--}),this},has:function(e){return e?w.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l};function I(e){return e}function W(e){throw e}function $(e,t,n,r){var i;try{e&&g(i=e.promise)?i.call(e).done(t).fail(n):e&&g(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}w.extend({Deferred:function(t){var n=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},"catch":function(e){return i.then(null,e)},pipe:function(){var e=arguments;return w.Deferred(function(t){w.each(n,function(n,r){var i=g(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&g(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(t,r,i){var o=0;function a(t,n,r,i){return function(){var s=this,u=arguments,l=function(){var e,l;if(!(t<o)){if((e=r.apply(s,u))===n.promise())throw new TypeError("Thenable self-resolution");l=e&&("object"==typeof e||"function"==typeof e)&&e.then,g(l)?i?l.call(e,a(o,n,I,i),a(o,n,W,i)):(o++,l.call(e,a(o,n,I,i),a(o,n,W,i),a(o,n,I,n.notifyWith))):(r!==I&&(s=void 0,u=[e]),(i||n.resolveWith)(s,u))}},c=i?l:function(){try{l()}catch(e){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(e,c.stackTrace),t+1>=o&&(r!==W&&(s=void 0,u=[e]),n.rejectWith(s,u))}};t?c():(w.Deferred.getStackHook&&(c.stackTrace=w.Deferred.getStackHook()),e.setTimeout(c))}}return w.Deferred(function(e){n[0][3].add(a(0,e,g(i)?i:I,e.notifyWith)),n[1][3].add(a(0,e,g(t)?t:I)),n[2][3].add(a(0,e,g(r)?r:W))}).promise()},promise:function(e){return null!=e?w.extend(e,i):i}},o={};return w.each(n,function(e,t){var a=t[2],s=t[5];i[t[1]]=a.add,s&&a.add(function(){r=s},n[3-e][2].disable,n[3-e][3].disable,n[0][2].lock,n[0][3].lock),a.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=a.fireWith}),i.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=o.call(arguments),a=w.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?o.call(arguments):n,--t||a.resolveWith(r,i)}};if(t<=1&&($(e,a.done(s(n)).resolve,a.reject,!t),"pending"===a.state()||g(i[n]&&i[n].then)))return a.then();while(n--)$(i[n],s(n),a.reject);return a.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&B.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},w.readyException=function(t){e.setTimeout(function(){throw t})};var F=w.Deferred();w.fn.ready=function(e){return F.then(e)["catch"](function(e){w.readyException(e)}),this},w.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--w.readyWait:w.isReady)||(w.isReady=!0,!0!==e&&--w.readyWait>0||F.resolveWith(r,[w]))}}),w.ready.then=F.then;function _(){r.removeEventListener("DOMContentLoaded",_),e.removeEventListener("load",_),w.ready()}"complete"===r.readyState||"loading"!==r.readyState&&!r.documentElement.doScroll?e.setTimeout(w.ready):(r.addEventListener("DOMContentLoaded",_),e.addEventListener("load",_));var z=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===x(n)){i=!0;for(s in n)z(e,t,s,n[s],!0,o,a)}else if(void 0!==r&&(i=!0,g(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(w(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},X=/^-ms-/,U=/-([a-z])/g;function V(e,t){return t.toUpperCase()}function G(e){return e.replace(X,"ms-").replace(U,V)}var Y=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Q(){this.expando=w.expando+Q.uid++}Q.uid=1,Q.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Y(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[G(t)]=n;else for(r in t)i[G(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][G(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(G):(t=G(t))in r?[t]:t.match(M)||[]).length;while(n--)delete r[t[n]]}(void 0===t||w.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!w.isEmptyObject(t)}};var J=new Q,K=new Q,Z=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ee=/[A-Z]/g;function te(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:Z.test(e)?JSON.parse(e):e)}function ne(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ee,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=te(n)}catch(e){}K.set(e,t,n)}else n=void 0;return n}w.extend({hasData:function(e){return K.hasData(e)||J.hasData(e)},data:function(e,t,n){return K.access(e,t,n)},removeData:function(e,t){K.remove(e,t)},_data:function(e,t,n){return J.access(e,t,n)},_removeData:function(e,t){J.remove(e,t)}}),w.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=K.get(o),1===o.nodeType&&!J.get(o,"hasDataAttrs"))){n=a.length;while(n--)a[n]&&0===(r=a[n].name).indexOf("data-")&&(r=G(r.slice(5)),ne(o,r,i[r]));J.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){K.set(this,e)}):z(this,function(t){var n;if(o&&void 0===t){if(void 0!==(n=K.get(o,e)))return n;if(void 0!==(n=ne(o,e)))return n}else this.each(function(){K.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){K.remove(this,e)})}}),w.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=J.get(e,t),n&&(!r||Array.isArray(n)?r=J.access(e,t,w.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=w.queue(e,t),r=n.length,i=n.shift(),o=w._queueHooks(e,t),a=function(){w.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return J.get(e,n)||J.access(e,n,{empty:w.Callbacks("once memory").add(function(){J.remove(e,[t+"queue",n])})})}}),w.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?w.queue(this[0],e):void 0===t?this:this.each(function(){var n=w.queue(this,e,t);w._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&w.dequeue(this,e)})},dequeue:function(e){return this.each(function(){w.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=w.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(a--)(n=J.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var re=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ie=new RegExp("^(?:([+-])=|)("+re+")([a-z%]*)$","i"),oe=["Top","Right","Bottom","Left"],ae=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&w.contains(e.ownerDocument,e)&&"none"===w.css(e,"display")},se=function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=a[o];return i};function ue(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return w.css(e,t,"")},u=s(),l=n&&n[3]||(w.cssNumber[t]?"":"px"),c=(w.cssNumber[t]||"px"!==l&&+u)&&ie.exec(w.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)w.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,w.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var le={};function ce(e){var t,n=e.ownerDocument,r=e.nodeName,i=le[r];return i||(t=n.body.appendChild(n.createElement(r)),i=w.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),le[r]=i,i)}function fe(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=J.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ae(r)&&(i[o]=ce(r))):"none"!==n&&(i[o]="none",J.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}w.fn.extend({show:function(){return fe(this,!0)},hide:function(){return fe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ae(this)?w(this).show():w(this).hide()})}});var pe=/^(?:checkbox|radio)$/i,de=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,he=/^$|^module$|\/(?:java|ecma)script/i,ge={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ge.optgroup=ge.option,ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td;function ye(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&N(e,t)?w.merge([e],n):n}function ve(e,t){for(var n=0,r=e.length;n<r;n++)J.set(e[n],"globalEval",!t||J.get(t[n],"globalEval"))}var me=/<|&#?\w+;/;function xe(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===x(o))w.merge(p,o.nodeType?[o]:o);else if(me.test(o)){a=a||f.appendChild(t.createElement("div")),s=(de.exec(o)||["",""])[1].toLowerCase(),u=ge[s]||ge._default,a.innerHTML=u[1]+w.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;w.merge(p,a.childNodes),(a=f.firstChild).textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&w.inArray(o,r)>-1)i&&i.push(o);else if(l=w.contains(o.ownerDocument,o),a=ye(f.appendChild(o),"script"),l&&ve(a),n){c=0;while(o=a[c++])he.test(o.type||"")&&n.push(o)}return f}!function(){var e=r.createDocumentFragment().appendChild(r.createElement("div")),t=r.createElement("input");t.setAttribute("type","radio"),t.setAttribute("checked","checked"),t.setAttribute("name","t"),e.appendChild(t),h.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,e.innerHTML="<textarea>x</textarea>",h.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue}();var be=r.documentElement,we=/^key/,Te=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ce=/^([^.]*)(?:\.(.+)|)/;function Ee(){return!0}function ke(){return!1}function Se(){try{return r.activeElement}catch(e){}}function De(e,t,n,r,i,o){var a,s;if("object"==typeof t){"string"!=typeof n&&(r=r||n,n=void 0);for(s in t)De(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=ke;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return w().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=w.guid++)),e.each(function(){w.event.add(this,t,i,r,n)})}w.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,y=J.get(e);if(y){n.handler&&(n=(o=n).handler,i=o.selector),i&&w.find.matchesSelector(be,i),n.guid||(n.guid=w.guid++),(u=y.events)||(u=y.events={}),(a=y.handle)||(a=y.handle=function(t){return"undefined"!=typeof w&&w.event.triggered!==t.type?w.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(M)||[""]).length;while(l--)d=g=(s=Ce.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=w.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=w.event.special[d]||{},c=w.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&w.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(e,r,h,a)||e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),w.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,y=J.hasData(e)&&J.get(e);if(y&&(u=y.events)){l=(t=(t||"").match(M)||[""]).length;while(l--)if(s=Ce.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d){f=w.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,y.handle)||w.removeEvent(e,d,y.handle),delete u[d])}else for(d in u)w.event.remove(e,d+t[l],n,r,!0);w.isEmptyObject(u)&&J.remove(e,"handle events")}},dispatch:function(e){var t=w.event.fix(e),n,r,i,o,a,s,u=new Array(arguments.length),l=(J.get(this,"events")||{})[t.type]||[],c=w.event.special[t.type]||{};for(u[0]=t,n=1;n<arguments.length;n++)u[n]=arguments[n];if(t.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,t)){s=w.event.handlers.call(this,t,l),n=0;while((o=s[n++])&&!t.isPropagationStopped()){t.currentTarget=o.elem,r=0;while((a=o.handlers[r++])&&!t.isImmediatePropagationStopped())t.rnamespace&&!t.rnamespace.test(a.namespace)||(t.handleObj=a,t.data=a.data,void 0!==(i=((w.event.special[a.origType]||{}).handle||a.handler).apply(o.elem,u))&&!1===(t.result=i)&&(t.preventDefault(),t.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?w(i,this).index(l)>-1:w.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(w.Event.prototype,e,{enumerable:!0,configurable:!0,get:g(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[w.expando]?e:new w.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==Se()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===Se()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&N(this,"input"))return this.click(),!1},_default:function(e){return N(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},w.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},w.Event=function(e,t){if(!(this instanceof w.Event))return new w.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ee:ke,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&w.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:ke,isPropagationStopped:ke,isImmediatePropagationStopped:ke,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ee,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ee,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ee,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&we.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&Te.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},w.event.addProp),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){w.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||w.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),w.fn.extend({on:function(e,t,n,r){return De(this,e,t,n,r)},one:function(e,t,n,r){return De(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,w(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=ke),this.each(function(){w.event.remove(this,e,n,t)})}});var Ne=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Ae=/<script|<style|<link/i,je=/checked\s*(?:[^=]|=\s*.checked.)/i,qe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Le(e,t){return N(e,"table")&&N(11!==t.nodeType?t:t.firstChild,"tr")?w(e).children("tbody")[0]||e:e}function He(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Oe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Pe(e,t){var n,r,i,o,a,s,u,l;if(1===t.nodeType){if(J.hasData(e)&&(o=J.access(e),a=J.set(t,o),l=o.events)){delete a.handle,a.events={};for(i in l)for(n=0,r=l[i].length;n<r;n++)w.event.add(t,i,l[i][n])}K.hasData(e)&&(s=K.access(e),u=w.extend({},s),K.set(t,u))}}function Me(e,t){var n=t.nodeName.toLowerCase();"input"===n&&pe.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Re(e,t,n,r){t=a.apply([],t);var i,o,s,u,l,c,f=0,p=e.length,d=p-1,y=t[0],v=g(y);if(v||p>1&&"string"==typeof y&&!h.checkClone&&je.test(y))return e.each(function(i){var o=e.eq(i);v&&(t[0]=y.call(this,i,o.html())),Re(o,t,n,r)});if(p&&(i=xe(t,e[0].ownerDocument,!1,e,r),o=i.firstChild,1===i.childNodes.length&&(i=o),o||r)){for(u=(s=w.map(ye(i,"script"),He)).length;f<p;f++)l=i,f!==d&&(l=w.clone(l,!0,!0),u&&w.merge(s,ye(l,"script"))),n.call(e[f],l,f);if(u)for(c=s[s.length-1].ownerDocument,w.map(s,Oe),f=0;f<u;f++)l=s[f],he.test(l.type||"")&&!J.access(l,"globalEval")&&w.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?w._evalUrl&&w._evalUrl(l.src):m(l.textContent.replace(qe,""),c,l))}return e}function Ie(e,t,n){for(var r,i=t?w.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||w.cleanData(ye(r)),r.parentNode&&(n&&w.contains(r.ownerDocument,r)&&ve(ye(r,"script")),r.parentNode.removeChild(r));return e}w.extend({htmlPrefilter:function(e){return e.replace(Ne,"<$1></$2>")},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),u=w.contains(e.ownerDocument,e);if(!(h.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||w.isXMLDoc(e)))for(a=ye(s),r=0,i=(o=ye(e)).length;r<i;r++)Me(o[r],a[r]);if(t)if(n)for(o=o||ye(e),a=a||ye(s),r=0,i=o.length;r<i;r++)Pe(o[r],a[r]);else Pe(e,s);return(a=ye(s,"script")).length>0&&ve(a,!u&&ye(e,"script")),s},cleanData:function(e){for(var t,n,r,i=w.event.special,o=0;void 0!==(n=e[o]);o++)if(Y(n)){if(t=n[J.expando]){if(t.events)for(r in t.events)i[r]?w.event.remove(n,r):w.removeEvent(n,r,t.handle);n[J.expando]=void 0}n[K.expando]&&(n[K.expando]=void 0)}}}),w.fn.extend({detach:function(e){return Ie(this,e,!0)},remove:function(e){return Ie(this,e)},text:function(e){return z(this,function(e){return void 0===e?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Re(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Le(this,e).appendChild(e)})},prepend:function(){return Re(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Le(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Re(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Re(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(w.cleanData(ye(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return w.clone(this,e,t)})},html:function(e){return z(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ae.test(e)&&!ge[(de.exec(e)||["",""])[1].toLowerCase()]){e=w.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(w.cleanData(ye(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return Re(this,arguments,function(t){var n=this.parentNode;w.inArray(this,e)<0&&(w.cleanData(ye(this)),n&&n.replaceChild(t,this))},e)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){w.fn[e]=function(e){for(var n,r=[],i=w(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),w(i[a])[t](n),s.apply(r,n.get());return this.pushStack(r)}});var We=new RegExp("^("+re+")(?!px)[a-z%]+$","i"),$e=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},Be=new RegExp(oe.join("|"),"i");!function(){function t(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",be.appendChild(l).appendChild(c);var t=e.getComputedStyle(c);i="1%"!==t.top,u=12===n(t.marginLeft),c.style.right="60%",s=36===n(t.right),o=36===n(t.width),c.style.position="absolute",a=36===c.offsetWidth||"absolute",be.removeChild(l),c=null}}function n(e){return Math.round(parseFloat(e))}var i,o,a,s,u,l=r.createElement("div"),c=r.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",h.clearCloneStyle="content-box"===c.style.backgroundClip,w.extend(h,{boxSizingReliable:function(){return t(),o},pixelBoxStyles:function(){return t(),s},pixelPosition:function(){return t(),i},reliableMarginLeft:function(){return t(),u},scrollboxSize:function(){return t(),a}}))}();function Fe(e,t,n){var r,i,o,a,s=e.style;return(n=n||$e(e))&&(""!==(a=n.getPropertyValue(t)||n[t])||w.contains(e.ownerDocument,e)||(a=w.style(e,t)),!h.pixelBoxStyles()&&We.test(a)&&Be.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0!==a?a+"":a}function _e(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}var ze=/^(none|table(?!-c[ea]).+)/,Xe=/^--/,Ue={position:"absolute",visibility:"hidden",display:"block"},Ve={letterSpacing:"0",fontWeight:"400"},Ge=["Webkit","Moz","ms"],Ye=r.createElement("div").style;function Qe(e){if(e in Ye)return e;var t=e[0].toUpperCase()+e.slice(1),n=Ge.length;while(n--)if((e=Ge[n]+t)in Ye)return e}function Je(e){var t=w.cssProps[e];return t||(t=w.cssProps[e]=Qe(e)||e),t}function Ke(e,t,n){var r=ie.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function Ze(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=w.css(e,n+oe[a],!0,i)),r?("content"===n&&(u-=w.css(e,"padding"+oe[a],!0,i)),"margin"!==n&&(u-=w.css(e,"border"+oe[a]+"Width",!0,i))):(u+=w.css(e,"padding"+oe[a],!0,i),"padding"!==n?u+=w.css(e,"border"+oe[a]+"Width",!0,i):s+=w.css(e,"border"+oe[a]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))),u}function et(e,t,n){var r=$e(e),i=Fe(e,t,r),o="border-box"===w.css(e,"boxSizing",!1,r),a=o;if(We.test(i)){if(!n)return i;i="auto"}return a=a&&(h.boxSizingReliable()||i===e.style[t]),("auto"===i||!parseFloat(i)&&"inline"===w.css(e,"display",!1,r))&&(i=e["offset"+t[0].toUpperCase()+t.slice(1)],a=!0),(i=parseFloat(i)||0)+Ze(e,t,n||(o?"border":"content"),a,r,i)+"px"}w.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Fe(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=G(t),u=Xe.test(t),l=e.style;if(u||(t=Je(s)),a=w.cssHooks[t]||w.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"==(o=typeof n)&&(i=ie.exec(n))&&i[1]&&(n=ue(e,t,i),o="number"),null!=n&&n===n&&("number"===o&&(n+=i&&i[3]||(w.cssNumber[s]?"":"px")),h.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=G(t);return Xe.test(t)||(t=Je(s)),(a=w.cssHooks[t]||w.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Fe(e,t,r)),"normal"===i&&t in Ve&&(i=Ve[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),w.each(["height","width"],function(e,t){w.cssHooks[t]={get:function(e,n,r){if(n)return!ze.test(w.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?et(e,t,r):se(e,Ue,function(){return et(e,t,r)})},set:function(e,n,r){var i,o=$e(e),a="border-box"===w.css(e,"boxSizing",!1,o),s=r&&Ze(e,t,r,a,o);return a&&h.scrollboxSize()===o.position&&(s-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-Ze(e,t,"border",!1,o)-.5)),s&&(i=ie.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=w.css(e,t)),Ke(e,n,s)}}}),w.cssHooks.marginLeft=_e(h.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Fe(e,"marginLeft"))||e.getBoundingClientRect().left-se(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(e,t){w.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+oe[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(w.cssHooks[e+t].set=Ke)}),w.fn.extend({css:function(e,t){return z(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=$e(e),i=t.length;a<i;a++)o[t[a]]=w.css(e,t[a],!1,r);return o}return void 0!==n?w.style(e,t,n):w.css(e,t)},e,t,arguments.length>1)}});function tt(e,t,n,r,i){return new tt.prototype.init(e,t,n,r,i)}w.Tween=tt,tt.prototype={constructor:tt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||w.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(w.cssNumber[n]?"":"px")},cur:function(){var e=tt.propHooks[this.prop];return e&&e.get?e.get(this):tt.propHooks._default.get(this)},run:function(e){var t,n=tt.propHooks[this.prop];return this.options.duration?this.pos=t=w.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):tt.propHooks._default.set(this),this}},tt.prototype.init.prototype=tt.prototype,tt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=w.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){w.fx.step[e.prop]?w.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[w.cssProps[e.prop]]&&!w.cssHooks[e.prop]?e.elem[e.prop]=e.now:w.style(e.elem,e.prop,e.now+e.unit)}}},tt.propHooks.scrollTop=tt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},w.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},w.fx=tt.prototype.init,w.fx.step={};var nt,rt,it=/^(?:toggle|show|hide)$/,ot=/queueHooks$/;function at(){rt&&(!1===r.hidden&&e.requestAnimationFrame?e.requestAnimationFrame(at):e.setTimeout(at,w.fx.interval),w.fx.tick())}function st(){return e.setTimeout(function(){nt=void 0}),nt=Date.now()}function ut(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=oe[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function lt(e,t,n){for(var r,i=(pt.tweeners[t]||[]).concat(pt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function ct(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ae(e),y=J.get(e,"fxshow");n.queue||(null==(a=w._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,w.queue(e,"fx").length||a.empty.fire()})}));for(r in t)if(i=t[r],it.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!y||void 0===y[r])continue;g=!0}d[r]=y&&y[r]||w.style(e,r)}if((u=!w.isEmptyObject(t))||!w.isEmptyObject(d)){f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=y&&y.display)&&(l=J.get(e,"display")),"none"===(c=w.css(e,"display"))&&(l?c=l:(fe([e],!0),l=e.style.display||l,c=w.css(e,"display"),fe([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===w.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1;for(r in d)u||(y?"hidden"in y&&(g=y.hidden):y=J.access(e,"fxshow",{display:l}),o&&(y.hidden=!g),g&&fe([e],!0),p.done(function(){g||fe([e]),J.remove(e,"fxshow");for(r in d)w.style(e,r,d[r])})),u=lt(g?y[r]:0,r,p),r in y||(y[r]=u.start,g&&(u.end=u.start,u.start=0))}}function ft(e,t){var n,r,i,o,a;for(n in e)if(r=G(n),i=t[r],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=w.cssHooks[r])&&"expand"in a){o=a.expand(o),delete e[r];for(n in o)n in e||(e[n]=o[n],t[n]=i)}else t[r]=i}function pt(e,t,n){var r,i,o=0,a=pt.prefilters.length,s=w.Deferred().always(function(){delete u.elem}),u=function(){if(i)return!1;for(var t=nt||st(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),o=0,a=l.tweens.length;o<a;o++)l.tweens[o].run(r);return s.notifyWith(e,[l,r,n]),r<1&&a?n:(a||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:w.extend({},t),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},n),originalProperties:t,originalOptions:n,startTime:nt||st(),duration:n.duration,tweens:[],createTween:function(t,n){var r=w.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(ft(c,l.opts.specialEasing);o<a;o++)if(r=pt.prefilters[o].call(l,e,c,l.opts))return g(r.stop)&&(w._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return w.map(c,lt,l),g(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),w.fx.timer(w.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}w.Animation=w.extend(pt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ue(n.elem,e,ie.exec(t),n),n}]},tweener:function(e,t){g(e)?(t=e,e=["*"]):e=e.match(M);for(var n,r=0,i=e.length;r<i;r++)n=e[r],pt.tweeners[n]=pt.tweeners[n]||[],pt.tweeners[n].unshift(t)},prefilters:[ct],prefilter:function(e,t){t?pt.prefilters.unshift(e):pt.prefilters.push(e)}}),w.speed=function(e,t,n){var r=e&&"object"==typeof e?w.extend({},e):{complete:n||!n&&t||g(e)&&e,duration:e,easing:n&&t||t&&!g(t)&&t};return w.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in w.fx.speeds?r.duration=w.fx.speeds[r.duration]:r.duration=w.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){g(r.old)&&r.old.call(this),r.queue&&w.dequeue(this,r.queue)},r},w.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ae).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=w.isEmptyObject(e),o=w.speed(t,n,r),a=function(){var t=pt(this,w.extend({},e),o);(i||J.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=w.timers,a=J.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&ot.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||w.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=J.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=w.timers,a=r?r.length:0;for(n.finish=!0,w.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),w.each(["toggle","show","hide"],function(e,t){var n=w.fn[t];w.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(ut(t,!0),e,r,i)}}),w.each({slideDown:ut("show"),slideUp:ut("hide"),slideToggle:ut("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){w.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),w.timers=[],w.fx.tick=function(){var e,t=0,n=w.timers;for(nt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||w.fx.stop(),nt=void 0},w.fx.timer=function(e){w.timers.push(e),w.fx.start()},w.fx.interval=13,w.fx.start=function(){rt||(rt=!0,at())},w.fx.stop=function(){rt=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(t,n){return t=w.fx?w.fx.speeds[t]||t:t,n=n||"fx",this.queue(n,function(n,r){var i=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(i)}})},function(){var e=r.createElement("input"),t=r.createElement("select").appendChild(r.createElement("option"));e.type="checkbox",h.checkOn=""!==e.value,h.optSelected=t.selected,(e=r.createElement("input")).value="t",e.type="radio",h.radioValue="t"===e.value}();var dt,ht=w.expr.attrHandle;w.fn.extend({attr:function(e,t){return z(this,w.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){w.removeAttr(this,e)})}}),w.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?w.prop(e,t,n):(1===o&&w.isXMLDoc(e)||(i=w.attrHooks[t.toLowerCase()]||(w.expr.match.bool.test(t)?dt:void 0)),void 0!==n?null===n?void w.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=w.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!h.radioValue&&"radio"===t&&N(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(M);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),dt={set:function(e,t,n){return!1===t?w.removeAttr(e,n):e.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(e,t){var n=ht[t]||w.find.attr;ht[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=ht[a],ht[a]=i,i=null!=n(e,t,r)?a:null,ht[a]=o),i}});var gt=/^(?:input|select|textarea|button)$/i,yt=/^(?:a|area)$/i;w.fn.extend({prop:function(e,t){return z(this,w.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[w.propFix[e]||e]})}}),w.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&w.isXMLDoc(e)||(t=w.propFix[t]||t,i=w.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=w.find.attr(e,"tabindex");return t?parseInt(t,10):gt.test(e.nodeName)||yt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),h.optSelected||(w.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this});function vt(e){return(e.match(M)||[]).join(" ")}function mt(e){return e.getAttribute&&e.getAttribute("class")||""}function xt(e){return Array.isArray(e)?e:"string"==typeof e?e.match(M)||[]:[]}w.fn.extend({addClass:function(e){var t,n,r,i,o,a,s,u=0;if(g(e))return this.each(function(t){w(this).addClass(e.call(this,t,mt(this)))});if((t=xt(e)).length)while(n=this[u++])if(i=mt(n),r=1===n.nodeType&&" "+vt(i)+" "){a=0;while(o=t[a++])r.indexOf(" "+o+" ")<0&&(r+=o+" ");i!==(s=vt(r))&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,i,o,a,s,u=0;if(g(e))return this.each(function(t){w(this).removeClass(e.call(this,t,mt(this)))});if(!arguments.length)return this.attr("class","");if((t=xt(e)).length)while(n=this[u++])if(i=mt(n),r=1===n.nodeType&&" "+vt(i)+" "){a=0;while(o=t[a++])while(r.indexOf(" "+o+" ")>-1)r=r.replace(" "+o+" "," ");i!==(s=vt(r))&&n.setAttribute("class",s)}return this},toggleClass:function(e,t){var n=typeof e,r="string"===n||Array.isArray(e);return"boolean"==typeof t&&r?t?this.addClass(e):this.removeClass(e):g(e)?this.each(function(n){w(this).toggleClass(e.call(this,n,mt(this),t),t)}):this.each(function(){var t,i,o,a;if(r){i=0,o=w(this),a=xt(e);while(t=a[i++])o.hasClass(t)?o.removeClass(t):o.addClass(t)}else void 0!==e&&"boolean"!==n||((t=mt(this))&&J.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":J.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&(" "+vt(mt(n))+" ").indexOf(t)>-1)return!0;return!1}});var bt=/\r/g;w.fn.extend({val:function(e){var t,n,r,i=this[0];{if(arguments.length)return r=g(e),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,w(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=w.map(i,function(e){return null==e?"":e+""})),(t=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))});if(i)return(t=w.valHooks[i.type]||w.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(bt,""):null==n?"":n}}}),w.extend({valHooks:{option:{get:function(e){var t=w.find.attr(e,"value");return null!=t?t:vt(w.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!N(n.parentNode,"optgroup"))){if(t=w(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=w.makeArray(t),a=i.length;while(a--)((r=i[a]).selected=w.inArray(w.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=w.inArray(w(e).val(),t)>-1}},h.checkOn||(w.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),h.focusin="onfocusin"in e;var wt=/^(?:focusinfocus|focusoutblur)$/,Tt=function(e){e.stopPropagation()};w.extend(w.event,{trigger:function(t,n,i,o){var a,s,u,l,c,p,d,h,v=[i||r],m=f.call(t,"type")?t.type:t,x=f.call(t,"namespace")?t.namespace.split("."):[];if(s=h=u=i=i||r,3!==i.nodeType&&8!==i.nodeType&&!wt.test(m+w.event.triggered)&&(m.indexOf(".")>-1&&(m=(x=m.split(".")).shift(),x.sort()),c=m.indexOf(":")<0&&"on"+m,t=t[w.expando]?t:new w.Event(m,"object"==typeof t&&t),t.isTrigger=o?2:3,t.namespace=x.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+x.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:w.makeArray(n,[t]),d=w.event.special[m]||{},o||!d.trigger||!1!==d.trigger.apply(i,n))){if(!o&&!d.noBubble&&!y(i)){for(l=d.delegateType||m,wt.test(l+m)||(s=s.parentNode);s;s=s.parentNode)v.push(s),u=s;u===(i.ownerDocument||r)&&v.push(u.defaultView||u.parentWindow||e)}a=0;while((s=v[a++])&&!t.isPropagationStopped())h=s,t.type=a>1?l:d.bindType||m,(p=(J.get(s,"events")||{})[t.type]&&J.get(s,"handle"))&&p.apply(s,n),(p=c&&s[c])&&p.apply&&Y(s)&&(t.result=p.apply(s,n),!1===t.result&&t.preventDefault());return t.type=m,o||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(v.pop(),n)||!Y(i)||c&&g(i[m])&&!y(i)&&((u=i[c])&&(i[c]=null),w.event.triggered=m,t.isPropagationStopped()&&h.addEventListener(m,Tt),i[m](),t.isPropagationStopped()&&h.removeEventListener(m,Tt),w.event.triggered=void 0,u&&(i[c]=u)),t.result}},simulate:function(e,t,n){var r=w.extend(new w.Event,n,{type:e,isSimulated:!0});w.event.trigger(r,null,t)}}),w.fn.extend({trigger:function(e,t){return this.each(function(){w.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return w.event.trigger(e,t,n,!0)}}),h.focusin||w.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){w.event.simulate(t,e.target,w.event.fix(e))};w.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=J.access(r,t);i||r.addEventListener(e,n,!0),J.access(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=J.access(r,t)-1;i?J.access(r,t,i):(r.removeEventListener(e,n,!0),J.remove(r,t))}}});var Ct=e.location,Et=Date.now(),kt=/\?/;w.parseXML=function(t){var n;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+t),n};var St=/\[\]$/,Dt=/\r?\n/g,Nt=/^(?:submit|button|image|reset|file)$/i,At=/^(?:input|select|textarea|keygen)/i;function jt(e,t,n,r){var i;if(Array.isArray(t))w.each(t,function(t,i){n||St.test(e)?r(e,i):jt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)});else if(n||"object"!==x(t))r(e,t);else for(i in t)jt(e+"["+i+"]",t[i],n,r)}w.param=function(e,t){var n,r=[],i=function(e,t){var n=g(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(Array.isArray(e)||e.jquery&&!w.isPlainObject(e))w.each(e,function(){i(this.name,this.value)});else for(n in e)jt(n,e[n],t,i);return r.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=w.prop(this,"elements");return e?w.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!w(this).is(":disabled")&&At.test(this.nodeName)&&!Nt.test(e)&&(this.checked||!pe.test(e))}).map(function(e,t){var n=w(this).val();return null==n?null:Array.isArray(n)?w.map(n,function(e){return{name:t.name,value:e.replace(Dt,"\r\n")}}):{name:t.name,value:n.replace(Dt,"\r\n")}}).get()}});var qt=/%20/g,Lt=/#.*$/,Ht=/([?&])_=[^&]*/,Ot=/^(.*?):[ \t]*([^\r\n]*)$/gm,Pt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Mt=/^(?:GET|HEAD)$/,Rt=/^\/\//,It={},Wt={},$t="*/".concat("*"),Bt=r.createElement("a");Bt.href=Ct.href;function Ft(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(M)||[];if(g(n))while(r=o[i++])"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function _t(e,t,n,r){var i={},o=e===Wt;function a(s){var u;return i[s]=!0,w.each(e[s]||[],function(e,s){var l=s(t,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),a(l),!1)}),u}return a(t.dataTypes[0])||!i["*"]&&a("*")}function zt(e,t){var n,r,i=w.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&w.extend(!0,e,r),e}function Xt(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}function Ut(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ct.href,type:"GET",isLocal:Pt.test(Ct.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":$t,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?zt(zt(e,w.ajaxSettings),t):zt(w.ajaxSettings,e)},ajaxPrefilter:Ft(It),ajaxTransport:Ft(Wt),ajax:function(t,n){"object"==typeof t&&(n=t,t=void 0),n=n||{};var i,o,a,s,u,l,c,f,p,d,h=w.ajaxSetup({},n),g=h.context||h,y=h.context&&(g.nodeType||g.jquery)?w(g):w.event,v=w.Deferred(),m=w.Callbacks("once memory"),x=h.statusCode||{},b={},T={},C="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s){s={};while(t=Ot.exec(a))s[t[1].toLowerCase()]=t[2]}t=s[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return c?a:null},setRequestHeader:function(e,t){return null==c&&(e=T[e.toLowerCase()]=T[e.toLowerCase()]||e,b[e]=t),this},overrideMimeType:function(e){return null==c&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)E.always(e[E.status]);else for(t in e)x[t]=[x[t],e[t]];return this},abort:function(e){var t=e||C;return i&&i.abort(t),k(0,t),this}};if(v.promise(E),h.url=((t||h.url||Ct.href)+"").replace(Rt,Ct.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(M)||[""],null==h.crossDomain){l=r.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=Bt.protocol+"//"+Bt.host!=l.protocol+"//"+l.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=w.param(h.data,h.traditional)),_t(It,h,n,E),c)return E;(f=w.event&&h.global)&&0==w.active++&&w.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Mt.test(h.type),o=h.url.replace(Lt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(qt,"+")):(d=h.url.slice(o.length),h.data&&(h.processData||"string"==typeof h.data)&&(o+=(kt.test(o)?"&":"?")+h.data,delete h.data),!1===h.cache&&(o=o.replace(Ht,"$1"),d=(kt.test(o)?"&":"?")+"_="+Et+++d),h.url=o+d),h.ifModified&&(w.lastModified[o]&&E.setRequestHeader("If-Modified-Since",w.lastModified[o]),w.etag[o]&&E.setRequestHeader("If-None-Match",w.etag[o])),(h.data&&h.hasContent&&!1!==h.contentType||n.contentType)&&E.setRequestHeader("Content-Type",h.contentType),E.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+$t+"; q=0.01":""):h.accepts["*"]);for(p in h.headers)E.setRequestHeader(p,h.headers[p]);if(h.beforeSend&&(!1===h.beforeSend.call(g,E,h)||c))return E.abort();if(C="abort",m.add(h.complete),E.done(h.success),E.fail(h.error),i=_t(Wt,h,n,E)){if(E.readyState=1,f&&y.trigger("ajaxSend",[E,h]),c)return E;h.async&&h.timeout>0&&(u=e.setTimeout(function(){E.abort("timeout")},h.timeout));try{c=!1,i.send(b,k)}catch(e){if(c)throw e;k(-1,e)}}else k(-1,"No Transport");function k(t,n,r,s){var l,p,d,b,T,C=n;c||(c=!0,u&&e.clearTimeout(u),i=void 0,a=s||"",E.readyState=t>0?4:0,l=t>=200&&t<300||304===t,r&&(b=Xt(h,E,r)),b=Ut(h,b,E,l),l?(h.ifModified&&((T=E.getResponseHeader("Last-Modified"))&&(w.lastModified[o]=T),(T=E.getResponseHeader("etag"))&&(w.etag[o]=T)),204===t||"HEAD"===h.type?C="nocontent":304===t?C="notmodified":(C=b.state,p=b.data,l=!(d=b.error))):(d=C,!t&&C||(C="error",t<0&&(t=0))),E.status=t,E.statusText=(n||C)+"",l?v.resolveWith(g,[p,C,E]):v.rejectWith(g,[E,C,d]),E.statusCode(x),x=void 0,f&&y.trigger(l?"ajaxSuccess":"ajaxError",[E,h,l?p:d]),m.fireWith(g,[E,C]),f&&(y.trigger("ajaxComplete",[E,h]),--w.active||w.event.trigger("ajaxStop")))}return E},getJSON:function(e,t,n){return w.get(e,t,n,"json")},getScript:function(e,t){return w.get(e,void 0,t,"script")}}),w.each(["get","post"],function(e,t){w[t]=function(e,n,r,i){return g(n)&&(i=i||r,r=n,n=void 0),w.ajax(w.extend({url:e,type:t,dataType:i,data:n,success:r},w.isPlainObject(e)&&e))}}),w._evalUrl=function(e){return w.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,"throws":!0})},w.fn.extend({wrapAll:function(e){var t;return this[0]&&(g(e)&&(e=e.call(this[0])),t=w(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return g(e)?this.each(function(t){w(this).wrapInner(e.call(this,t))}):this.each(function(){var t=w(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=g(e);return this.each(function(n){w(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){w(this).replaceWith(this.childNodes)}),this}}),w.expr.pseudos.hidden=function(e){return!w.expr.pseudos.visible(e)},w.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var Vt={0:200,1223:204},Gt=w.ajaxSettings.xhr();h.cors=!!Gt&&"withCredentials"in Gt,h.ajax=Gt=!!Gt,w.ajaxTransport(function(t){var n,r;if(h.cors||Gt&&!t.crossDomain)return{send:function(i,o){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)s[a]=t.xhrFields[a];t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(a in i)s.setRequestHeader(a,i[a]);n=function(e){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Vt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout(function(){n&&r()})},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(e){if(n)throw e}},abort:function(){n&&n()}}}),w.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return w.globalEval(e),e}}}),w.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),w.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(i,o){t=w("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),r.head.appendChild(t[0])},abort:function(){n&&n()}}}});var Yt=[],Qt=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Yt.pop()||w.expando+"_"+Et++;return this[e]=!0,e}}),w.ajaxPrefilter("json jsonp",function(t,n,r){var i,o,a,s=!1!==t.jsonp&&(Qt.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Qt.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=g(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Qt,"$1"+i):!1!==t.jsonp&&(t.url+=(kt.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return a||w.error(i+" was not called"),a[0]},t.dataTypes[0]="json",o=e[i],e[i]=function(){a=arguments},r.always(function(){void 0===o?w(e).removeProp(i):e[i]=o,t[i]&&(t.jsonpCallback=n.jsonpCallback,Yt.push(i)),a&&g(o)&&o(a[0]),a=o=void 0}),"script"}),h.createHTMLDocument=function(){var e=r.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),w.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var i,o,a;return t||(h.createHTMLDocument?((i=(t=r.implementation.createHTMLDocument("")).createElement("base")).href=r.location.href,t.head.appendChild(i)):t=r),o=A.exec(e),a=!n&&[],o?[t.createElement(o[1])]:(o=xe([e],t,a),a&&a.length&&w(a).remove(),w.merge([],o.childNodes))},w.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=vt(e.slice(s)),e=e.slice(0,s)),g(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),a.length>0&&w.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?w("<div>").append(w.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){w.fn[t]=function(e){return this.on(t,e)}}),w.expr.pseudos.animated=function(e){return w.grep(w.timers,function(t){return e===t.elem}).length},w.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l,c=w.css(e,"position"),f=w(e),p={};"static"===c&&(e.style.position="relative"),s=f.offset(),o=w.css(e,"top"),u=w.css(e,"left"),(l=("absolute"===c||"fixed"===c)&&(o+u).indexOf("auto")>-1)?(a=(r=f.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),g(t)&&(t=t.call(e,n,w.extend({},s))),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+i),"using"in t?t.using.call(e,p):f.css(p)}},w.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){w.offset.setOffset(this,e,t)});var t,n,r=this[0];if(r)return r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===w.css(r,"position"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&"static"===w.css(e,"position"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=w(e).offset()).top+=w.css(e,"borderTopWidth",!0),i.left+=w.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-w.css(r,"marginTop",!0),left:t.left-i.left-w.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&"static"===w.css(e,"position"))e=e.offsetParent;return e||be})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;w.fn[e]=function(r){return z(this,function(e,r,i){var o;if(y(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i},e,r,arguments.length)}}),w.each(["top","left"],function(e,t){w.cssHooks[t]=_e(h.pixelPosition,function(e,n){if(n)return n=Fe(e,t),We.test(n)?w(e).position()[t]+"px":n})}),w.each({Height:"height",Width:"width"},function(e,t){w.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){w.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return z(this,function(t,n,i){var o;return y(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?w.css(t,n,s):w.style(t,n,i,s)},t,a?i:void 0,a)}})}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){w.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),w.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),w.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),w.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),g(e))return r=o.call(arguments,2),i=function(){return e.apply(t||this,r.concat(o.call(arguments)))},i.guid=e.guid=e.guid||w.guid++,i},w.holdReady=function(e){e?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=N,w.isFunction=g,w.isWindow=y,w.camelCase=G,w.type=x,w.now=Date.now,w.isNumeric=function(e){var t=w.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Jt=e.jQuery,Kt=e.$;return w.noConflict=function(t){return e.$===w&&(e.$=Kt),t&&e.jQuery===w&&(e.jQuery=Jt),w},t||(e.jQuery=e.$=w),w});
</script>
<script type="text/javascript">
/*
 * jQuery Templates Plugin 1.0.0pre
 * http://github.com/jquery/jquery-tmpl
 * Requires jQuery 1.4.2
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 */
(function(a){var r=a.fn.domManip,d="_tmplitem",q=/^[^<]*(<[\w\W]+>)[^>]*$|\{\{\! /,b={},f={},e,p={key:0,data:{}},i=0,c=0,l=[];function g(g,d,h,e){var c={data:e||(e===0||e===false)?e:d?d.data:{},_wrap:d?d._wrap:null,tmpl:null,parent:d||null,nodes:[],calls:u,nest:w,wrap:x,html:v,update:t};g&&a.extend(c,g,{nodes:[],parent:d});if(h){c.tmpl=h;c._ctnt=c._ctnt||c.tmpl(a,c);c.key=++i;(l.length?f:b)[i]=c}return c}a.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(f,d){a.fn[f]=function(n){var g=[],i=a(n),k,h,m,l,j=this.length===1&&this[0].parentNode;e=b||{};if(j&&j.nodeType===11&&j.childNodes.length===1&&i.length===1){i[d](this[0]);g=this}else{for(h=0,m=i.length;h<m;h++){c=h;k=(h>0?this.clone(true):this).get();a(i[h])[d](k);g=g.concat(k)}c=0;g=this.pushStack(g,f,i.selector)}l=e;e=null;a.tmpl.complete(l);return g}});a.fn.extend({tmpl:function(d,c,b){return a.tmpl(this[0],d,c,b)},tmplItem:function(){return a.tmplItem(this[0])},template:function(b){return a.template(b,this[0])},domManip:function(d,m,k){if(d[0]&&a.isArray(d[0])){var g=a.makeArray(arguments),h=d[0],j=h.length,i=0,f;while(i<j&&!(f=a.data(h[i++],"tmplItem")));if(f&&c)g[2]=function(b){a.tmpl.afterManip(this,b,k)};r.apply(this,g)}else r.apply(this,arguments);c=0;!e&&a.tmpl.complete(b);return this}});a.extend({tmpl:function(d,h,e,c){var i,k=!c;if(k){c=p;d=a.template[d]||a.template(null,d);f={}}else if(!d){d=c.tmpl;b[c.key]=c;c.nodes=[];c.wrapped&&n(c,c.wrapped);return a(j(c,null,c.tmpl(a,c)))}if(!d)return[];if(typeof h==="function")h=h.call(c||{});e&&e.wrapped&&n(e,e.wrapped);i=a.isArray(h)?a.map(h,function(a){return a?g(e,c,d,a):null}):[g(e,c,d,h)];return k?a(j(c,null,i)):i},tmplItem:function(b){var c;if(b instanceof a)b=b[0];while(b&&b.nodeType===1&&!(c=a.data(b,"tmplItem"))&&(b=b.parentNode));return c||p},template:function(c,b){if(b){if(typeof b==="string")b=o(b);else if(b instanceof a)b=b[0]||{};if(b.nodeType)b=a.data(b,"tmpl")||a.data(b,"tmpl",o(b.innerHTML));return typeof c==="string"?(a.template[c]=b):b}return c?typeof c!=="string"?a.template(null,c):a.template[c]||a.template(null,q.test(c)?c:a(c)):null},encode:function(a){return(""+a).split("&").join("&amp;").split("<").join("&lt;").split(">").join("&gt;").split('"').join("&#34;").split("'").join("&#39;")}});a.extend(a.tmpl,{tag:{tmpl:{_default:{$2:"null"},open:"if($notnull_1){__=__.concat($item.nest($1,$2));}"},wrap:{_default:{$2:"null"},open:"$item.calls(__,$1,$2);__=[];",close:"call=$item.calls();__=call._.concat($item.wrap(call,__));"},each:{_default:{$2:"$index, $value"},open:"if($notnull_1){$.each($1a,function($2){with(this){",close:"}});}"},"if":{open:"if(($notnull_1) && $1a){",close:"}"},"else":{_default:{$1:"true"},open:"}else if(($notnull_1) && $1a){"},html:{open:"if($notnull_1){__.push($1a);}"},"=":{_default:{$1:"$data"},open:"if($notnull_1){__.push($.encode($1a));}"},"!":{open:""}},complete:function(){b={}},afterManip:function(f,b,d){var e=b.nodeType===11?a.makeArray(b.childNodes):b.nodeType===1?[b]:[];d.call(f,b);m(e);c++}});function j(e,g,f){var b,c=f?a.map(f,function(a){return typeof a==="string"?e.key?a.replace(/(<\w+)(?=[\s>])(?![^>]*_tmplitem)([^>]*)/g,"$1 "+d+'="'+e.key+'" $2'):a:j(a,e,a._ctnt)}):e;if(g)return c;c=c.join("");c.replace(/^\s*([^<\s][^<]*)?(<[\w\W]+>)([^>]*[^>\s])?\s*$/,function(f,c,e,d){b=a(e).get();m(b);if(c)b=k(c).concat(b);if(d)b=b.concat(k(d))});return b?b:k(c)}function k(c){var b=document.createElement("div");b.innerHTML=c;return a.makeArray(b.childNodes)}function o(b){return new Function("jQuery","$item","var $=jQuery,call,__=[],$data=$item.data;with($data){__.push('"+a.trim(b).replace(/([\\'])/g,"\\$1").replace(/[\r\t\n]/g," ").replace(/\$\{([^\}]*)\}/g,"{{= $1}}").replace(/\{\{(\/?)(\w+|.)(?:\(((?:[^\}]|\}(?!\}))*?)?\))?(?:\s+(.*?)?)?(\(((?:[^\}]|\}(?!\}))*?)\))?\s*\}\}/g,function(m,l,k,g,b,c,d){var j=a.tmpl.tag[k],i,e,f;if(!j)throw"Unknown template tag: "+k;i=j._default||[];if(c&&!/\w$/.test(b)){b+=c;c=""}if(b){b=h(b);d=d?","+h(d)+")":c?")":"";e=c?b.indexOf(".")>-1?b+h(c):"("+b+").call($item"+d:b;f=c?e:"(typeof("+b+")==='function'?("+b+").call($item):("+b+"))"}else f=e=i.$1||"null";g=h(g);return"');"+j[l?"close":"open"].split("$notnull_1").join(b?"typeof("+b+")!=='undefined' && ("+b+")!=null":"true").split("$1a").join(f).split("$1").join(e).split("$2").join(g||i.$2||"")+"__.push('"})+"');}return __;")}function n(c,b){c._wrap=j(c,true,a.isArray(b)?b:[q.test(b)?b:a(b).html()]).join("")}function h(a){return a?a.replace(/\\'/g,"'").replace(/\\\\/g,"\\"):null}function s(b){var a=document.createElement("div");a.appendChild(b.cloneNode(true));return a.innerHTML}function m(o){var n="_"+c,k,j,l={},e,p,h;for(e=0,p=o.length;e<p;e++){if((k=o[e]).nodeType!==1)continue;j=k.getElementsByTagName("*");for(h=j.length-1;h>=0;h--)m(j[h]);m(k)}function m(j){var p,h=j,k,e,m;if(m=j.getAttribute(d)){while(h.parentNode&&(h=h.parentNode).nodeType===1&&!(p=h.getAttribute(d)));if(p!==m){h=h.parentNode?h.nodeType===11?0:h.getAttribute(d)||0:0;if(!(e=b[m])){e=f[m];e=g(e,b[h]||f[h]);e.key=++i;b[i]=e}c&&o(m)}j.removeAttribute(d)}else if(c&&(e=a.data(j,"tmplItem"))){o(e.key);b[e.key]=e;h=a.data(j.parentNode,"tmplItem");h=h?h.key:0}if(e){k=e;while(k&&k.key!=h){k.nodes.push(j);k=k.parent}delete e._ctnt;delete e._wrap;a.data(j,"tmplItem",e)}function o(a){a=a+n;e=l[a]=l[a]||g(e,b[e.parent.key+n]||e.parent)}}}function u(a,d,c,b){if(!a)return l.pop();l.push({_:a,tmpl:d,item:this,data:c,options:b})}function w(d,c,b){return a.tmpl(a.template(d),c,b,this)}function x(b,d){var c=b.options||{};c.wrapped=d;return a.tmpl(a.template(b.tmpl),b.data,c,b.item)}function v(d,c){var b=this._wrap;return a.map(a(a.isArray(b)?b.join(""):b).filter(d||"*"),function(a){return c?a.innerText||a.textContent:a.outerHTML||s(a)})}function t(){var b=this.nodes;a.tmpl(null,null,null,this).insertBefore(b[0]);a(b).remove()}})(jQuery);
</script>
<script type="text/javascript">
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&"object"==typeof module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){return function(e){"use strict";var t=e.tablesorter={version:"2.30.5",parsers:[],widgets:[],defaults:{theme:"default",widthFixed:!1,showProcessing:!1,headerTemplate:"{content}",onRenderTemplate:null,onRenderHeader:null,cancelSelection:!0,tabIndex:!0,dateFormat:"mmddyyyy",sortMultiSortKey:"shiftKey",sortResetKey:"ctrlKey",usNumberFormat:!0,delayInit:!1,serverSideSorting:!1,resort:!0,headers:{},ignoreCase:!0,sortForce:null,sortList:[],sortAppend:null,sortStable:!1,sortInitialOrder:"asc",sortLocaleCompare:!1,sortReset:!1,sortRestart:!1,emptyTo:"bottom",stringTo:"max",duplicateSpan:!0,textExtraction:"basic",textAttribute:"data-text",textSorter:null,numberSorter:null,initWidgets:!0,widgetClass:"widget-{name}",widgets:[],widgetOptions:{zebra:["even","odd"]},initialized:null,tableClass:"",cssAsc:"",cssDesc:"",cssNone:"",cssHeader:"",cssHeaderRow:"",cssProcessing:"",cssChildRow:"tablesorter-childRow",cssInfoBlock:"tablesorter-infoOnly",cssNoSort:"tablesorter-noSort",cssIgnoreRow:"tablesorter-ignoreRow",cssIcon:"tablesorter-icon",cssIconNone:"",cssIconAsc:"",cssIconDesc:"",cssIconDisabled:"",pointerClick:"click",pointerDown:"mousedown",pointerUp:"mouseup",selectorHeaders:"> thead th, > thead td",selectorSort:"th, td",selectorRemove:".remove-me",debug:!1,headerList:[],empties:{},strings:{},parsers:[],globalize:0,imgAttr:0},css:{table:"tablesorter",cssHasChild:"tablesorter-hasChildRow",childRow:"tablesorter-childRow",colgroup:"tablesorter-colgroup",header:"tablesorter-header",headerRow:"tablesorter-headerRow",headerIn:"tablesorter-header-inner",icon:"tablesorter-icon",processing:"tablesorter-processing",sortAsc:"tablesorter-headerAsc",sortDesc:"tablesorter-headerDesc",sortNone:"tablesorter-headerUnSorted"},language:{sortAsc:"Ascending sort applied, ",sortDesc:"Descending sort applied, ",sortNone:"No sort applied, ",sortDisabled:"sorting is disabled",nextAsc:"activate to apply an ascending sort",nextDesc:"activate to apply a descending sort",nextNone:"activate to remove the sort"},regex:{templateContent:/\{content\}/g,templateIcon:/\{icon\}/g,templateName:/\{name\}/i,spaces:/\s+/g,nonWord:/\W/g,formElements:/(input|select|button|textarea)/i,chunk:/(^([+\-]?(?:\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,chunks:/(^\\0|\\0$)/,hex:/^0x[0-9a-f]+$/i,comma:/,/g,digitNonUS:/[\s|\.]/g,digitNegativeTest:/^\s*\([.\d]+\)/,digitNegativeReplace:/^\s*\(([.\d]+)\)/,digitTest:/^[\-+(]?\d+[)]?$/,digitReplace:/[,.'"\s]/g},string:{max:1,min:-1,emptymin:1,emptymax:-1,zero:0,none:0,"null":0,top:!0,bottom:!1},keyCodes:{enter:13},dates:{},instanceMethods:{},setup:function(r,o){if(r&&r.tHead&&0!==r.tBodies.length&&!0!==r.hasInitialized){var s="",a=e(r),n=e.metadata;r.hasInitialized=!1,r.isProcessing=!0,r.config=o,e.data(r,"tablesorter",o),t.debug(o,"core")&&(console[console.group?"group":"log"]("Initializing tablesorter v"+t.version),e.data(r,"startoveralltimer",new Date)),o.supportsDataObject=function(e){return e[0]=parseInt(e[0],10),e[0]>1||1===e[0]&&parseInt(e[1],10)>=4}(e.fn.jquery.split(".")),o.emptyTo=o.emptyTo.toLowerCase(),o.stringTo=o.stringTo.toLowerCase(),o.last={sortList:[],clickedIndex:-1},/tablesorter\-/.test(a.attr("class"))||(s=""!==o.theme?" tablesorter-"+o.theme:""),o.namespace?o.namespace="."+o.namespace.replace(t.regex.nonWord,""):o.namespace=".tablesorter"+Math.random().toString(16).slice(2),o.table=r,o.$table=a.addClass(t.css.table+" "+o.tableClass+s+" "+o.namespace.slice(1)).attr("role","grid"),o.$headers=a.find(o.selectorHeaders),o.$table.children().children("tr").attr("role","row"),o.$tbodies=a.children("tbody:not(."+o.cssInfoBlock+")").attr({"aria-live":"polite","aria-relevant":"all"}),o.$table.children("caption").length&&((s=o.$table.children("caption")[0]).id||(s.id=o.namespace.slice(1)+"caption"),o.$table.attr("aria-labelledby",s.id)),o.widgetInit={},o.textExtraction=o.$table.attr("data-text-extraction")||o.textExtraction||"basic",t.buildHeaders(o),t.fixColumnWidth(r),t.addWidgetFromClass(r),t.applyWidgetOptions(r),t.setupParsers(o),o.totalRows=0,o.debug&&t.validateOptions(o),o.delayInit||t.buildCache(o),t.bindEvents(r,o.$headers,!0),t.bindMethods(o),o.supportsDataObject&&void 0!==a.data().sortlist?o.sortList=a.data().sortlist:n&&a.metadata()&&a.metadata().sortlist&&(o.sortList=a.metadata().sortlist),t.applyWidget(r,!0),o.sortList.length>0?t.sortOn(o,o.sortList,{},!o.initWidgets):(t.setHeadersCss(o),o.initWidgets&&t.applyWidget(r,!1)),o.showProcessing&&a.unbind("sortBegin"+o.namespace+" sortEnd"+o.namespace).bind("sortBegin"+o.namespace+" sortEnd"+o.namespace,function(e){clearTimeout(o.timerProcessing),t.isProcessing(r),"sortBegin"===e.type&&(o.timerProcessing=setTimeout(function(){t.isProcessing(r,!0)},500))}),r.hasInitialized=!0,r.isProcessing=!1,t.debug(o,"core")&&(console.log("Overall initialization time:"+t.benchmark(e.data(r,"startoveralltimer"))),t.debug(o,"core")&&console.groupEnd&&console.groupEnd()),a.triggerHandler("tablesorter-initialized",r),"function"==typeof o.initialized&&o.initialized(r)}else t.debug(o,"core")&&(r.hasInitialized?console.warn("Stopping initialization. Tablesorter has already been initialized"):console.error("Stopping initialization! No table, thead or tbody",r))},bindMethods:function(r){var o=r.$table,s=r.namespace,a="sortReset update updateRows updateAll updateHeaders addRows updateCell updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave ".split(" ").join(s+" ");o.unbind(a.replace(t.regex.spaces," ")).bind("sortReset"+s,function(e,r){e.stopPropagation(),t.sortReset(this.config,function(e){e.isApplyingWidgets?setTimeout(function(){t.applyWidget(e,"",r)},100):t.applyWidget(e,"",r)})}).bind("updateAll"+s,function(e,r,o){e.stopPropagation(),t.updateAll(this.config,r,o)}).bind("update"+s+" updateRows"+s,function(e,r,o){e.stopPropagation(),t.update(this.config,r,o)}).bind("updateHeaders"+s,function(e,r){e.stopPropagation(),t.updateHeaders(this.config,r)}).bind("updateCell"+s,function(e,r,o,s){e.stopPropagation(),t.updateCell(this.config,r,o,s)}).bind("addRows"+s,function(e,r,o,s){e.stopPropagation(),t.addRows(this.config,r,o,s)}).bind("updateComplete"+s,function(){this.isUpdating=!1}).bind("sorton"+s,function(e,r,o,s){e.stopPropagation(),t.sortOn(this.config,r,o,s)}).bind("appendCache"+s,function(r,o,s){r.stopPropagation(),t.appendCache(this.config,s),e.isFunction(o)&&o(this)}).bind("updateCache"+s,function(e,r,o){e.stopPropagation(),t.updateCache(this.config,r,o)}).bind("applyWidgetId"+s,function(e,r){e.stopPropagation(),t.applyWidgetId(this,r)}).bind("applyWidgets"+s,function(e,r){e.stopPropagation(),t.applyWidget(this,!1,r)}).bind("refreshWidgets"+s,function(e,r,o){e.stopPropagation(),t.refreshWidgets(this,r,o)}).bind("removeWidget"+s,function(e,r,o){e.stopPropagation(),t.removeWidget(this,r,o)}).bind("destroy"+s,function(e,r,o){e.stopPropagation(),t.destroy(this,r,o)}).bind("resetToLoadState"+s,function(o){o.stopPropagation(),t.removeWidget(this,!0,!1);var s=e.extend(!0,{},r.originalSettings);(r=e.extend(!0,{},t.defaults,s)).originalSettings=s,this.hasInitialized=!1,t.setup(this,r)})},bindEvents:function(r,o,s){var a,n=(r=e(r)[0]).config,i=n.namespace,l=null;!0!==s&&(o.addClass(i.slice(1)+"_extra_headers"),(a=t.getClosest(o,"table")).length&&"TABLE"===a[0].nodeName&&a[0]!==r&&e(a[0]).addClass(i.slice(1)+"_extra_table")),a=(n.pointerDown+" "+n.pointerUp+" "+n.pointerClick+" sort keyup ").replace(t.regex.spaces," ").split(" ").join(i+" "),o.find(n.selectorSort).add(o.filter(n.selectorSort)).unbind(a).bind(a,function(r,o){var s,a,i,d=e(r.target),c=" "+r.type+" ";if(!(1!==(r.which||r.button)&&!c.match(" "+n.pointerClick+" | sort | keyup ")||" keyup "===c&&r.which!==t.keyCodes.enter||c.match(" "+n.pointerClick+" ")&&void 0!==r.which||c.match(" "+n.pointerUp+" ")&&l!==r.target&&!0!==o)){if(c.match(" "+n.pointerDown+" "))return l=r.target,void("1"===(i=d.jquery.split("."))[0]&&i[1]<4&&r.preventDefault());if(l=null,s=t.getClosest(e(this),"."+t.css.header),t.regex.formElements.test(r.target.nodeName)||d.hasClass(n.cssNoSort)||d.parents("."+n.cssNoSort).length>0||s.hasClass("sorter-false")||d.parents("button").length>0)return!n.cancelSelection;n.delayInit&&t.isEmptyObject(n.cache)&&t.buildCache(n),n.last.clickedIndex=s.attr("data-column")||s.index(),(a=n.$headerIndexed[n.last.clickedIndex][0])&&!a.sortDisabled&&t.initSort(n,a,r)}}),n.cancelSelection&&o.attr("unselectable","on").bind("selectstart",!1).css({"user-select":"none",MozUserSelect:"none"})},buildHeaders:function(r){var o,s,a,n;for(r.headerList=[],r.headerContent=[],r.sortVars=[],t.debug(r,"core")&&(a=new Date),r.columns=t.computeColumnIndex(r.$table.children("thead, tfoot").children("tr")),s=r.cssIcon?'<i class="'+(r.cssIcon===t.css.icon?t.css.icon:r.cssIcon+" "+t.css.icon)+'"></i>':"",r.$headers=e(e.map(r.$table.find(r.selectorHeaders),function(o,a){var n,i,l,d,c,g=e(o);if(!t.getClosest(g,"tr").hasClass(r.cssIgnoreRow))return/(th|td)/i.test(o.nodeName)||(c=t.getClosest(g,"th, td"),g.attr("data-column",c.attr("data-column"))),n=t.getColumnData(r.table,r.headers,a,!0),r.headerContent[a]=g.html(),""===r.headerTemplate||g.find("."+t.css.headerIn).length||(d=r.headerTemplate.replace(t.regex.templateContent,g.html()).replace(t.regex.templateIcon,g.find("."+t.css.icon).length?"":s),r.onRenderTemplate&&(i=r.onRenderTemplate.apply(g,[a,d]))&&"string"==typeof i&&(d=i),g.html('<div class="'+t.css.headerIn+'">'+d+"</div>")),r.onRenderHeader&&r.onRenderHeader.apply(g,[a,r,r.$table]),l=parseInt(g.attr("data-column"),10),o.column=l,c=t.getOrder(t.getData(g,n,"sortInitialOrder")||r.sortInitialOrder),r.sortVars[l]={count:-1,order:c?r.sortReset?[1,0,2]:[1,0]:r.sortReset?[0,1,2]:[0,1],lockedOrder:!1},void 0!==(c=t.getData(g,n,"lockedOrder")||!1)&&!1!==c&&(r.sortVars[l].lockedOrder=!0,r.sortVars[l].order=t.getOrder(c)?[1,1]:[0,0]),r.headerList[a]=o,g.addClass(t.css.header+" "+r.cssHeader),t.getClosest(g,"tr").addClass(t.css.headerRow+" "+r.cssHeaderRow).attr("role","row"),r.tabIndex&&g.attr("tabindex",0),o})),r.$headerIndexed=[],n=0;n<r.columns;n++)t.isEmptyObject(r.sortVars[n])&&(r.sortVars[n]={}),o=r.$headers.filter('[data-column="'+n+'"]'),r.$headerIndexed[n]=o.length?o.not(".sorter-false").length?o.not(".sorter-false").filter(":last"):o.filter(":last"):e();r.$table.find(r.selectorHeaders).attr({scope:"col",role:"columnheader"}),t.updateHeader(r),t.debug(r,"core")&&(console.log("Built headers:"+t.benchmark(a)),console.log(r.$headers))},addInstanceMethods:function(r){e.extend(t.instanceMethods,r)},setupParsers:function(e,r){var o,s,a,n,i,l,d,c,g,p,u,f,h,m,b=e.table,y=0,w=t.debug(e,"core"),x={};if(e.$tbodies=e.$table.children("tbody:not(."+e.cssInfoBlock+")"),h=void 0===r?e.$tbodies:r,0===(m=h.length))return w?console.warn("Warning: *Empty table!* Not building a parser cache"):"";for(w&&(f=new Date,console[console.group?"group":"log"]("Detecting parsers for each column")),s={extractors:[],parsers:[]};y<m;){if((o=h[y].rows).length)for(i=0,n=e.columns,l=0;l<n;l++){if((d=e.$headerIndexed[i])&&d.length&&(c=t.getColumnData(b,e.headers,i),u=t.getParserById(t.getData(d,c,"extractor")),p=t.getParserById(t.getData(d,c,"sorter")),g="false"===t.getData(d,c,"parser"),e.empties[i]=(t.getData(d,c,"empty")||e.emptyTo||(e.emptyToBottom?"bottom":"top")).toLowerCase(),e.strings[i]=(t.getData(d,c,"string")||e.stringTo||"max").toLowerCase(),g&&(p=t.getParserById("no-parser")),u||(u=!1),p||(p=t.detectParserForColumn(e,o,-1,i)),w&&(x["("+i+") "+d.text()]={parser:p.id,extractor:u?u.id:"none",string:e.strings[i],empty:e.empties[i]}),s.parsers[i]=p,s.extractors[i]=u,(a=d[0].colSpan-1)>0))for(i+=a,n+=a;a+1>0;)s.parsers[i-a]=p,s.extractors[i-a]=u,a--;i++}y+=s.parsers.length?m:1}w&&(t.isEmptyObject(x)?console.warn("  No parsers detected!"):console[console.table?"table":"log"](x),console.log("Completed detecting parsers"+t.benchmark(f)),console.groupEnd&&console.groupEnd()),e.parsers=s.parsers,e.extractors=s.extractors},addParser:function(e){var r,o=t.parsers.length,s=!0;for(r=0;r<o;r++)t.parsers[r].id.toLowerCase()===e.id.toLowerCase()&&(s=!1);s&&(t.parsers[t.parsers.length]=e)},getParserById:function(e){if("false"==e)return!1;var r,o=t.parsers.length;for(r=0;r<o;r++)if(t.parsers[r].id.toLowerCase()===e.toString().toLowerCase())return t.parsers[r];return!1},detectParserForColumn:function(r,o,s,a){for(var n,i,l,d=t.parsers.length,c=!1,g="",p=t.debug(r,"core"),u=!0;""===g&&u;)(l=o[++s])&&s<50?l.className.indexOf(t.cssIgnoreRow)<0&&(c=o[s].cells[a],g=t.getElementText(r,c,a),i=e(c),p&&console.log("Checking if value was empty on row "+s+", column: "+a+': "'+g+'"')):u=!1;for(;--d>=0;)if((n=t.parsers[d])&&"text"!==n.id&&n.is&&n.is(g,r.table,c,i))return n;return t.getParserById("text")},getElementText:function(r,o,s){if(!o)return"";var a,n=r.textExtraction||"",i=o.jquery?o:e(o);return"string"==typeof n?"basic"===n&&void 0!==(a=i.attr(r.textAttribute))?e.trim(a):e.trim(o.textContent||i.text()):"function"==typeof n?e.trim(n(i[0],r.table,s)):"function"==typeof(a=t.getColumnData(r.table,n,s))?e.trim(a(i[0],r.table,s)):e.trim(i[0].textContent||i.text())},getParsedText:function(e,r,o,s){void 0===s&&(s=t.getElementText(e,r,o));var a=""+s,n=e.parsers[o],i=e.extractors[o];return n&&(i&&"function"==typeof i.format&&(s=i.format(s,e.table,r,o)),a="no-parser"===n.id?"":n.format(""+s,e.table,r,o),e.ignoreCase&&"string"==typeof a&&(a=a.toLowerCase())),a},buildCache:function(r,o,s){var a,n,i,l,d,c,g,p,u,f,h,m,b,y,w,x,v,C,$,I,D,R,T=r.table,L=r.parsers,A=t.debug(r,"core");if(r.$tbodies=r.$table.children("tbody:not(."+r.cssInfoBlock+")"),g=void 0===s?r.$tbodies:s,r.cache={},r.totalRows=0,!L)return A?console.warn("Warning: *Empty table!* Not building a cache"):"";for(A&&(m=new Date),r.showProcessing&&t.isProcessing(T,!0),c=0;c<g.length;c++){for(x=[],a=r.cache[c]={normalized:[]},b=g[c]&&g[c].rows.length||0,l=0;l<b;++l)if(y={child:[],raw:[]},p=e(g[c].rows[l]),u=[],!p.hasClass(r.selectorRemove.slice(1)))if(p.hasClass(r.cssChildRow)&&0!==l)for(D=a.normalized.length-1,(w=a.normalized[D][r.columns]).$row=w.$row.add(p),p.prev().hasClass(r.cssChildRow)||p.prev().addClass(t.css.cssHasChild),f=p.children("th, td"),D=w.child.length,w.child[D]=[],C=0,I=r.columns,d=0;d<I;d++)(h=f[d])&&(w.child[D][d]=t.getParsedText(r,h,d),(v=f[d].colSpan-1)>0&&(C+=v,I+=v)),C++;else{for(y.$row=p,y.order=l,C=0,I=r.columns,d=0;d<I;++d){if((h=p[0].cells[d])&&C<r.columns&&(!($=void 0!==L[C])&&A&&console.warn("No parser found for row: "+l+", column: "+d+'; cell containing: "'+e(h).text()+'"; does it have a header?'),n=t.getElementText(r,h,C),y.raw[C]=n,i=t.getParsedText(r,h,C,n),u[C]=i,$&&"numeric"===(L[C].type||"").toLowerCase()&&(x[C]=Math.max(Math.abs(i)||0,x[C]||0)),(v=h.colSpan-1)>0)){for(R=0;R<=v;)i=r.duplicateSpan||0===R?n:"string"!=typeof r.textExtraction?t.getElementText(r,h,C+R)||"":"",y.raw[C+R]=i,u[C+R]=i,R++;C+=v,I+=v}C++}u[r.columns]=y,a.normalized[a.normalized.length]=u}a.colMax=x,r.totalRows+=a.normalized.length}if(r.showProcessing&&t.isProcessing(T),A){for(D=Math.min(5,r.cache[0].normalized.length),console[console.group?"group":"log"]("Building cache for "+r.totalRows+" rows (showing "+D+" rows in log) and "+r.columns+" columns"+t.benchmark(m)),n={},d=0;d<r.columns;d++)for(C=0;C<D;C++)n["row: "+C]||(n["row: "+C]={}),n["row: "+C][r.$headerIndexed[d].text()]=r.cache[0].normalized[C][d];console[console.table?"table":"log"](n),console.groupEnd&&console.groupEnd()}e.isFunction(o)&&o(T)},getColumnText:function(r,o,s,a){var n,i,l,d,c,g,p,u,f,h,m="function"==typeof s,b="all"===o,y={raw:[],parsed:[],$cell:[]},w=(r=e(r)[0]).config;if(!t.isEmptyObject(w)){for(c=w.$tbodies.length,n=0;n<c;n++)for(g=(l=w.cache[n].normalized).length,i=0;i<g;i++)d=l[i],a&&!d[w.columns].$row.is(a)||(h=!0,u=b?d.slice(0,w.columns):d[o],d=d[w.columns],p=b?d.raw:d.raw[o],f=b?d.$row.children():d.$row.children().eq(o),m&&(h=s({tbodyIndex:n,rowIndex:i,parsed:u,raw:p,$row:d.$row,$cell:f})),!1!==h&&(y.parsed[y.parsed.length]=u,y.raw[y.raw.length]=p,y.$cell[y.$cell.length]=f));return y}t.debug(w,"core")&&console.warn("No cache found - aborting getColumnText function!")},setHeadersCss:function(r){var o,s,a=r.sortList,n=a.length,i=t.css.sortNone+" "+r.cssNone,l=[t.css.sortAsc+" "+r.cssAsc,t.css.sortDesc+" "+r.cssDesc],d=[r.cssIconAsc,r.cssIconDesc,r.cssIconNone],c=["ascending","descending"],g=function(e,r){e.removeClass(i).addClass(l[r]).attr("aria-sort",c[r]).find("."+t.css.icon).removeClass(d[2]).addClass(d[r])},p=r.$table.find("tfoot tr").children("td, th").add(e(r.namespace+"_extra_headers")).removeClass(l.join(" ")),u=r.$headers.add(e("thead "+r.namespace+"_extra_headers")).removeClass(l.join(" ")).addClass(i).attr("aria-sort","none").find("."+t.css.icon).removeClass(d.join(" ")).end();for(u.not(".sorter-false").find("."+t.css.icon).addClass(d[2]),r.cssIconDisabled&&u.filter(".sorter-false").find("."+t.css.icon).addClass(r.cssIconDisabled),o=0;o<n;o++)if(2!==a[o][1]){if(u=r.$headers.filter(function(e){for(var o=!0,s=r.$headers.eq(e),a=parseInt(s.attr("data-column"),10),n=a+t.getClosest(s,"th, td")[0].colSpan;a<n;a++)o=!!o&&(o||t.isValueInArray(a,r.sortList)>-1);return o}),(u=u.not(".sorter-false").filter('[data-column="'+a[o][0]+'"]'+(1===n?":last":""))).length)for(s=0;s<u.length;s++)u[s].sortDisabled||g(u.eq(s),a[o][1]);p.length&&g(p.filter('[data-column="'+a[o][0]+'"]'),a[o][1])}for(n=r.$headers.length,o=0;o<n;o++)t.setColumnAriaLabel(r,r.$headers.eq(o))},getClosest:function(t,r){return e.fn.closest?t.closest(r):t.is(r)?t:t.parents(r).filter(":first")},setColumnAriaLabel:function(r,o,s){if(o.length){var a=parseInt(o.attr("data-column"),10),n=r.sortVars[a],i=o.hasClass(t.css.sortAsc)?"sortAsc":o.hasClass(t.css.sortDesc)?"sortDesc":"sortNone",l=e.trim(o.text())+": "+t.language[i];o.hasClass("sorter-false")||!1===s?l+=t.language.sortDisabled:(i=(n.count+1)%n.order.length,s=n.order[i],l+=t.language[0===s?"nextAsc":1===s?"nextDesc":"nextNone"]),o.attr("aria-label",l)}},updateHeader:function(e){var r,o,s,a,n=e.table,i=e.$headers.length;for(r=0;r<i;r++)s=e.$headers.eq(r),a=t.getColumnData(n,e.headers,r,!0),o="false"===t.getData(s,a,"sorter")||"false"===t.getData(s,a,"parser"),t.setColumnSort(e,s,o)},setColumnSort:function(e,t,r){var o=e.table.id;t[0].sortDisabled=r,t[r?"addClass":"removeClass"]("sorter-false").attr("aria-disabled",""+r),e.tabIndex&&(r?t.removeAttr("tabindex"):t.attr("tabindex","0")),o&&(r?t.removeAttr("aria-controls"):t.attr("aria-controls",o))},updateHeaderSortCount:function(r,o){var s,a,n,i,l,d,c,g,p=o||r.sortList,u=p.length;for(r.sortList=[],i=0;i<u;i++)if(c=p[i],(s=parseInt(c[0],10))<r.columns){switch(r.sortVars[s].order||(g=t.getOrder(r.sortInitialOrder)?r.sortReset?[1,0,2]:[1,0]:r.sortReset?[0,1,2]:[0,1],r.sortVars[s].order=g,r.sortVars[s].count=0),g=r.sortVars[s].order,a=(""+c[1]).match(/^(1|d|s|o|n)/),a=a?a[0]:""){case"1":case"d":a=1;break;case"s":a=l||0;break;case"o":a=0===(d=g[(l||0)%g.length])?1:1===d?0:2;break;case"n":a=g[++r.sortVars[s].count%g.length];break;default:a=0}l=0===i?a:l,n=[s,parseInt(a,10)||0],r.sortList[r.sortList.length]=n,a=e.inArray(n[1],g),r.sortVars[s].count=a>=0?a:n[1]%g.length}},updateAll:function(e,r,o){var s=e.table;s.isUpdating=!0,t.refreshWidgets(s,!0,!0),t.buildHeaders(e),t.bindEvents(s,e.$headers,!0),t.bindMethods(e),t.commonUpdate(e,r,o)},update:function(e,r,o){e.table.isUpdating=!0,t.updateHeader(e),t.commonUpdate(e,r,o)},updateHeaders:function(e,r){e.table.isUpdating=!0,t.buildHeaders(e),t.bindEvents(e.table,e.$headers,!0),t.resortComplete(e,r)},updateCell:function(r,o,s,a){if(e(o).closest("tr").hasClass(r.cssChildRow))console.warn('Tablesorter Warning! "updateCell" for child row content has been disabled, use "update" instead');else{if(t.isEmptyObject(r.cache))return t.updateHeader(r),void t.commonUpdate(r,s,a);r.table.isUpdating=!0,r.$table.find(r.selectorRemove).remove();var n,i,l,d,c,g,p=r.$tbodies,u=e(o),f=p.index(t.getClosest(u,"tbody")),h=r.cache[f],m=t.getClosest(u,"tr");if(o=u[0],p.length&&f>=0){if(l=p.eq(f).find("tr").not("."+r.cssChildRow).index(m),c=h.normalized[l],(g=m[0].cells.length)!==r.columns)for(d=0,n=!1,i=0;i<g;i++)n||m[0].cells[i]===o?n=!0:d+=m[0].cells[i].colSpan;else d=u.index();n=t.getElementText(r,o,d),c[r.columns].raw[d]=n,n=t.getParsedText(r,o,d,n),c[d]=n,"numeric"===(r.parsers[d].type||"").toLowerCase()&&(h.colMax[d]=Math.max(Math.abs(n)||0,h.colMax[d]||0)),!1!==(n="undefined"!==s?s:r.resort)?t.checkResort(r,n,a):t.resortComplete(r,a)}else t.debug(r,"core")&&console.error("updateCell aborted, tbody missing or not within the indicated table"),r.table.isUpdating=!1}},addRows:function(r,o,s,a){var n,i,l,d,c,g,p,u,f,h,m,b,y,w="string"==typeof o&&1===r.$tbodies.length&&/<tr/.test(o||""),x=r.table;if(w)o=e(o),r.$tbodies.append(o);else if(!(o&&o instanceof e&&t.getClosest(o,"table")[0]===r.table))return t.debug(r,"core")&&console.error("addRows method requires (1) a jQuery selector reference to rows that have already been added to the table, or (2) row HTML string to be added to a table with only one tbody"),!1;if(x.isUpdating=!0,t.isEmptyObject(r.cache))t.updateHeader(r),t.commonUpdate(r,s,a);else{for(c=o.filter("tr").attr("role","row").length,l=r.$tbodies.index(o.parents("tbody").filter(":first")),r.parsers&&r.parsers.length||t.setupParsers(r),d=0;d<c;d++){for(f=0,p=o[d].cells.length,u=r.cache[l].normalized.length,m=[],h={child:[],raw:[],$row:o.eq(d),order:u},g=0;g<p;g++)b=o[d].cells[g],n=t.getElementText(r,b,f),h.raw[f]=n,i=t.getParsedText(r,b,f,n),m[f]=i,"numeric"===(r.parsers[f].type||"").toLowerCase()&&(r.cache[l].colMax[f]=Math.max(Math.abs(i)||0,r.cache[l].colMax[f]||0)),(y=b.colSpan-1)>0&&(f+=y),f++;m[r.columns]=h,r.cache[l].normalized[u]=m}t.checkResort(r,s,a)}},updateCache:function(e,r,o){e.parsers&&e.parsers.length||t.setupParsers(e,o),t.buildCache(e,r,o)},appendCache:function(e,r){var o,s,a,n,i,l,d,c=e.table,g=e.$tbodies,p=[],u=e.cache;if(t.isEmptyObject(u))return e.appender?e.appender(c,p):c.isUpdating?e.$table.triggerHandler("updateComplete",c):"";for(t.debug(e,"core")&&(d=new Date),l=0;l<g.length;l++)if((a=g.eq(l)).length){for(n=t.processTbody(c,a,!0),s=(o=u[l].normalized).length,i=0;i<s;i++)p[p.length]=o[i][e.columns].$row,e.appender&&(!e.pager||e.pager.removeRows||e.pager.ajax)||n.append(o[i][e.columns].$row);t.processTbody(c,n,!1)}e.appender&&e.appender(c,p),t.debug(e,"core")&&console.log("Rebuilt table"+t.benchmark(d)),r||e.appender||t.applyWidget(c),c.isUpdating&&e.$table.triggerHandler("updateComplete",c)},commonUpdate:function(e,r,o){e.$table.find(e.selectorRemove).remove(),t.setupParsers(e),t.buildCache(e),t.checkResort(e,r,o)},initSort:function(r,o,s){if(r.table.isUpdating)return setTimeout(function(){t.initSort(r,o,s)},50);var a,n,i,l,d,c,g,p=!s[r.sortMultiSortKey],u=r.table,f=r.$headers.length,h=t.getClosest(e(o),"th, td"),m=parseInt(h.attr("data-column"),10),b=r.sortVars[m].order;if(h=h[0],r.$table.triggerHandler("sortStart",u),c=(r.sortVars[m].count+1)%b.length,r.sortVars[m].count=s[r.sortResetKey]?2:c,r.sortRestart)for(i=0;i<f;i++)g=r.$headers.eq(i),m!==(c=parseInt(g.attr("data-column"),10))&&(p||g.hasClass(t.css.sortNone))&&(r.sortVars[c].count=-1);if(p){if(r.sortList=[],r.last.sortList=[],null!==r.sortForce)for(a=r.sortForce,n=0;n<a.length;n++)a[n][0]!==m&&(r.sortList[r.sortList.length]=a[n]);if((l=b[r.sortVars[m].count])<2&&(r.sortList[r.sortList.length]=[m,l],h.colSpan>1))for(n=1;n<h.colSpan;n++)r.sortList[r.sortList.length]=[m+n,l],r.sortVars[m+n].count=e.inArray(l,b)}else if(r.sortList=e.extend([],r.last.sortList),t.isValueInArray(m,r.sortList)>=0)for(n=0;n<r.sortList.length;n++)(c=r.sortList[n])[0]===m&&(c[1]=b[r.sortVars[m].count],2===c[1]&&(r.sortList.splice(n,1),r.sortVars[m].count=-1));else if((l=b[r.sortVars[m].count])<2&&(r.sortList[r.sortList.length]=[m,l],h.colSpan>1))for(n=1;n<h.colSpan;n++)r.sortList[r.sortList.length]=[m+n,l],r.sortVars[m+n].count=e.inArray(l,b);if(r.last.sortList=e.extend([],r.sortList),r.sortList.length&&r.sortAppend&&(a=e.isArray(r.sortAppend)?r.sortAppend:r.sortAppend[r.sortList[0][0]],!t.isEmptyObject(a)))for(n=0;n<a.length;n++)if(a[n][0]!==m&&t.isValueInArray(a[n][0],r.sortList)<0){if(l=a[n][1],d=(""+l).match(/^(a|d|s|o|n)/))switch(c=r.sortList[0][1],d[0]){case"d":l=1;break;case"s":l=c;break;case"o":l=0===c?1:0;break;case"n":l=(c+1)%b.length;break;default:l=0}r.sortList[r.sortList.length]=[a[n][0],l]}r.$table.triggerHandler("sortBegin",u),setTimeout(function(){t.setHeadersCss(r),t.multisort(r),t.appendCache(r),r.$table.triggerHandler("sortBeforeEnd",u),r.$table.triggerHandler("sortEnd",u)},1)},multisort:function(e){var r,o,s,a,n=e.table,i=[],l=0,d=e.textSorter||"",c=e.sortList,g=c.length,p=e.$tbodies.length;if(!e.serverSideSorting&&!t.isEmptyObject(e.cache)){if(t.debug(e,"core")&&(o=new Date),"object"==typeof d)for(s=e.columns;s--;)"function"==typeof(a=t.getColumnData(n,d,s))&&(i[s]=a);for(r=0;r<p;r++)s=e.cache[r].colMax,e.cache[r].normalized.sort(function(r,o){var a,p,u,f,h,m,b;for(a=0;a<g;a++){if(u=c[a][0],f=c[a][1],l=0===f,e.sortStable&&r[u]===o[u]&&1===g)return r[e.columns].order-o[e.columns].order;if(p=/n/i.test(t.getSortType(e.parsers,u)),p&&e.strings[u]?(p="boolean"==typeof t.string[e.strings[u]]?(l?1:-1)*(t.string[e.strings[u]]?-1:1):e.strings[u]?t.string[e.strings[u]]||0:0,h=e.numberSorter?e.numberSorter(r[u],o[u],l,s[u],n):t["sortNumeric"+(l?"Asc":"Desc")](r[u],o[u],p,s[u],u,e)):(m=l?r:o,b=l?o:r,h="function"==typeof d?d(m[u],b[u],l,u,n):"function"==typeof i[u]?i[u](m[u],b[u],l,u,n):t["sortNatural"+(l?"Asc":"Desc")](r[u],o[u],u,e)),h)return h}return r[e.columns].order-o[e.columns].order});t.debug(e,"core")&&console.log("Applying sort "+c.toString()+t.benchmark(o))}},resortComplete:function(t,r){t.table.isUpdating&&t.$table.triggerHandler("updateComplete",t.table),e.isFunction(r)&&r(t.table)},checkResort:function(r,o,s){var a=e.isArray(o)?o:r.sortList;!1===(void 0===o?r.resort:o)||r.serverSideSorting||r.table.isProcessing?(t.resortComplete(r,s),t.applyWidget(r.table,!1)):a.length?t.sortOn(r,a,function(){t.resortComplete(r,s)},!0):t.sortReset(r,function(){t.resortComplete(r,s),t.applyWidget(r.table,!1)})},sortOn:function(r,o,s,a){var n=r.table;r.$table.triggerHandler("sortStart",n),t.updateHeaderSortCount(r,o),t.setHeadersCss(r),r.delayInit&&t.isEmptyObject(r.cache)&&t.buildCache(r),r.$table.triggerHandler("sortBegin",n),t.multisort(r),t.appendCache(r,a),r.$table.triggerHandler("sortBeforeEnd",n),r.$table.triggerHandler("sortEnd",n),t.applyWidget(n),e.isFunction(s)&&s(n)},sortReset:function(r,o){r.sortList=[],t.setHeadersCss(r),t.multisort(r),t.appendCache(r);var s;for(s=0;s<r.columns;s++)r.sortVars[s].count=-1;e.isFunction(o)&&o(r.table)},getSortType:function(e,t){return e&&e[t]?e[t].type||"":""},getOrder:function(e){return/^d/i.test(e)||1===e},sortNatural:function(e,r){if(e===r)return 0;e=e.toString(),r=r.toString();var o,s,a,n,i,l,d=t.regex;if(d.hex.test(r)){if(o=parseInt((e||"").match(d.hex),16),s=parseInt((r||"").match(d.hex),16),o<s)return-1;if(o>s)return 1}for(o=(e||"").replace(d.chunk,"\\0$1\\0").replace(d.chunks,"").split("\\0"),s=(r||"").replace(d.chunk,"\\0$1\\0").replace(d.chunks,"").split("\\0"),l=Math.max(o.length,s.length),i=0;i<l;i++){if(a=isNaN(o[i])?o[i]||0:parseFloat(o[i])||0,n=isNaN(s[i])?s[i]||0:parseFloat(s[i])||0,isNaN(a)!==isNaN(n))return isNaN(a)?1:-1;if(typeof a!=typeof n&&(a+="",n+=""),a<n)return-1;if(a>n)return 1}return 0},sortNaturalAsc:function(e,r,o,s){if(e===r)return 0;var a=t.string[s.empties[o]||s.emptyTo];return""===e&&0!==a?"boolean"==typeof a?a?-1:1:-a||-1:""===r&&0!==a?"boolean"==typeof a?a?1:-1:a||1:t.sortNatural(e,r)},sortNaturalDesc:function(e,r,o,s){if(e===r)return 0;var a=t.string[s.empties[o]||s.emptyTo];return""===e&&0!==a?"boolean"==typeof a?a?-1:1:a||1:""===r&&0!==a?"boolean"==typeof a?a?1:-1:-a||-1:t.sortNatural(r,e)},sortText:function(e,t){return e>t?1:e<t?-1:0},getTextValue:function(e,t,r){if(r){var o,s=e?e.length:0,a=r+t;for(o=0;o<s;o++)a+=e.charCodeAt(o);return t*a}return 0},sortNumericAsc:function(e,r,o,s,a,n){if(e===r)return 0;var i=t.string[n.empties[a]||n.emptyTo];return""===e&&0!==i?"boolean"==typeof i?i?-1:1:-i||-1:""===r&&0!==i?"boolean"==typeof i?i?1:-1:i||1:(isNaN(e)&&(e=t.getTextValue(e,o,s)),isNaN(r)&&(r=t.getTextValue(r,o,s)),e-r)},sortNumericDesc:function(e,r,o,s,a,n){if(e===r)return 0;var i=t.string[n.empties[a]||n.emptyTo];return""===e&&0!==i?"boolean"==typeof i?i?-1:1:i||1:""===r&&0!==i?"boolean"==typeof i?i?1:-1:-i||-1:(isNaN(e)&&(e=t.getTextValue(e,o,s)),isNaN(r)&&(r=t.getTextValue(r,o,s)),r-e)},sortNumeric:function(e,t){return e-t},addWidget:function(e){e.id&&!t.isEmptyObject(t.getWidgetById(e.id))&&console.warn('"'+e.id+'" widget was loaded more than once!'),t.widgets[t.widgets.length]=e},hasWidget:function(t,r){return(t=e(t)).length&&t[0].config&&t[0].config.widgetInit[r]||!1},getWidgetById:function(e){var r,o,s=t.widgets.length;for(r=0;r<s;r++)if((o=t.widgets[r])&&o.id&&o.id.toLowerCase()===e.toLowerCase())return o},applyWidgetOptions:function(r){var o,s,a,n=r.config,i=n.widgets.length;if(i)for(o=0;o<i;o++)(s=t.getWidgetById(n.widgets[o]))&&s.options&&(a=e.extend(!0,{},s.options),n.widgetOptions=e.extend(!0,a,n.widgetOptions),e.extend(!0,t.defaults.widgetOptions,s.options))},addWidgetFromClass:function(e){var r,o,s=e.config,a="^"+s.widgetClass.replace(t.regex.templateName,"(\\S+)+")+"$",n=new RegExp(a,"g"),i=(e.className||"").split(t.regex.spaces);if(i.length)for(r=i.length,o=0;o<r;o++)i[o].match(n)&&(s.widgets[s.widgets.length]=i[o].replace(n,"$1"))},applyWidgetId:function(r,o,s){var a,n,i,l=(r=e(r)[0]).config,d=l.widgetOptions,c=t.debug(l,"core"),g=t.getWidgetById(o);g&&(i=g.id,a=!1,e.inArray(i,l.widgets)<0&&(l.widgets[l.widgets.length]=i),c&&(n=new Date),!s&&l.widgetInit[i]||(l.widgetInit[i]=!0,r.hasInitialized&&t.applyWidgetOptions(r),"function"==typeof g.init&&(a=!0,c&&console[console.group?"group":"log"]("Initializing "+i+" widget"),g.init(r,g,l,d))),s||"function"!=typeof g.format||(a=!0,c&&console[console.group?"group":"log"]("Updating "+i+" widget"),g.format(r,l,d,!1)),c&&a&&(console.log("Completed "+(s?"initializing ":"applying ")+i+" widget"+t.benchmark(n)),console.groupEnd&&console.groupEnd()))},applyWidget:function(r,o,s){var a,n,i,l,d,c=(r=e(r)[0]).config,g=t.debug(c,"core"),p=[];if(!1===o||!r.hasInitialized||!r.isApplyingWidgets&&!r.isUpdating){if(g&&(d=new Date),t.addWidgetFromClass(r),clearTimeout(c.timerReady),c.widgets.length){for(r.isApplyingWidgets=!0,c.widgets=e.grep(c.widgets,function(t,r){return e.inArray(t,c.widgets)===r}),n=(i=c.widgets||[]).length,a=0;a<n;a++)(l=t.getWidgetById(i[a]))&&l.id?(l.priority||(l.priority=10),p[a]=l):g&&console.warn('"'+i[a]+'" was enabled, but the widget code has not been loaded!');for(p.sort(function(e,t){return e.priority<t.priority?-1:e.priority===t.priority?0:1}),n=p.length,g&&console[console.group?"group":"log"]("Start "+(o?"initializing":"applying")+" widgets"),a=0;a<n;a++)(l=p[a])&&l.id&&t.applyWidgetId(r,l.id,o);g&&console.groupEnd&&console.groupEnd()}c.timerReady=setTimeout(function(){r.isApplyingWidgets=!1,e.data(r,"lastWidgetApplication",new Date),c.$table.triggerHandler("tablesorter-ready"),o||"function"!=typeof s||s(r),g&&(l=c.widgets.length,console.log("Completed "+(!0===o?"initializing ":"applying ")+l+" widget"+(1!==l?"s":"")+t.benchmark(d)))},10)}},removeWidget:function(r,o,s){var a,n,i,l,d=(r=e(r)[0]).config;if(!0===o)for(o=[],l=t.widgets.length,i=0;i<l;i++)(n=t.widgets[i])&&n.id&&(o[o.length]=n.id);else o=(e.isArray(o)?o.join(","):o||"").toLowerCase().split(/[\s,]+/);for(l=o.length,a=0;a<l;a++)n=t.getWidgetById(o[a]),(i=e.inArray(o[a],d.widgets))>=0&&!0!==s&&d.widgets.splice(i,1),n&&n.remove&&(t.debug(d,"core")&&console.log((s?"Refreshing":"Removing")+' "'+o[a]+'" widget'),n.remove(r,d,d.widgetOptions,s),d.widgetInit[o[a]]=!1);d.$table.triggerHandler("widgetRemoveEnd",r)},refreshWidgets:function(r,o,s){var a,n,i=(r=e(r)[0]).config.widgets,l=t.widgets,d=l.length,c=[],g=function(t){e(t).triggerHandler("refreshComplete")};for(a=0;a<d;a++)(n=l[a])&&n.id&&(o||e.inArray(n.id,i)<0)&&(c[c.length]=n.id);t.removeWidget(r,c.join(","),!0),!0!==s?(t.applyWidget(r,o||!1,g),o&&t.applyWidget(r,!1,g)):g(r)},benchmark:function(e){return" ("+((new Date).getTime()-e.getTime())+" ms)"},log:function(){console.log(arguments)},debug:function(e,t){return e&&(!0===e.debug||"string"==typeof e.debug&&e.debug.indexOf(t)>-1)},isEmptyObject:function(e){for(var t in e)return!1;return!0},isValueInArray:function(e,t){var r,o=t&&t.length||0;for(r=0;r<o;r++)if(t[r][0]===e)return r;return-1},formatFloat:function(r,o){if("string"!=typeof r||""===r)return r;var s;return r=(o&&o.config?!1!==o.config.usNumberFormat:void 0===o||o)?r.replace(t.regex.comma,""):r.replace(t.regex.digitNonUS,"").replace(t.regex.comma,"."),t.regex.digitNegativeTest.test(r)&&(r=r.replace(t.regex.digitNegativeReplace,"-$1")),s=parseFloat(r),isNaN(s)?e.trim(r):s},isDigit:function(e){return isNaN(e)?t.regex.digitTest.test(e.toString().replace(t.regex.digitReplace,"")):""!==e},computeColumnIndex:function(r,o){var s,a,n,i,l,d,c,g,p,u,f=o&&o.columns||0,h=[],m=new Array(f);for(s=0;s<r.length;s++)for(d=r[s].cells,a=0;a<d.length;a++){for(c=s,g=(l=d[a]).rowSpan||1,p=l.colSpan||1,void 0===h[c]&&(h[c]=[]),n=0;n<h[c].length+1;n++)if(void 0===h[c][n]){u=n;break}for(f&&l.cellIndex===u||(l.setAttribute?l.setAttribute("data-column",u):e(l).attr("data-column",u)),n=c;n<c+g;n++)for(void 0===h[n]&&(h[n]=[]),m=h[n],i=u;i<u+p;i++)m[i]="x"}return t.checkColumnCount(r,h,m.length),m.length},checkColumnCount:function(e,t,r){var o,s,a=!0,n=[];for(o=0;o<t.length;o++)if(t[o]&&(s=t[o].length,t[o].length!==r)){a=!1;break}a||(e.each(function(e,t){var r=t.parentElement.nodeName;n.indexOf(r)<0&&n.push(r)}),console.error("Invalid or incorrect number of columns in the "+n.join(" or ")+"; expected "+r+", but found "+s+" columns"))},fixColumnWidth:function(r){var o,s,a,n,i,l=(r=e(r)[0]).config,d=l.$table.children("colgroup");if(d.length&&d.hasClass(t.css.colgroup)&&d.remove(),l.widthFixed&&0===l.$table.children("colgroup").length){for(d=e('<colgroup class="'+t.css.colgroup+'">'),o=l.$table.width(),n=(a=l.$tbodies.find("tr:first").children(":visible")).length,i=0;i<n;i++)s=parseInt(a.eq(i).width()/o*1e3,10)/10+"%",d.append(e("<col>").css("width",s));l.$table.prepend(d)}},getData:function(t,r,o){var s,a,n="",i=e(t);return i.length?(s=!!e.metadata&&i.metadata(),a=" "+(i.attr("class")||""),void 0!==i.data(o)||void 0!==i.data(o.toLowerCase())?n+=i.data(o)||i.data(o.toLowerCase()):s&&void 0!==s[o]?n+=s[o]:r&&void 0!==r[o]?n+=r[o]:" "!==a&&a.match(" "+o+"-")&&(n=a.match(new RegExp("\\s"+o+"-([\\w-]+)"))[1]||""),e.trim(n)):""},getColumnData:function(t,r,o,s,a){if("object"!=typeof r||null===r)return r;var n,i=(t=e(t)[0]).config,l=a||i.$headers,d=i.$headerIndexed&&i.$headerIndexed[o]||l.find('[data-column="'+o+'"]:last');if(void 0!==r[o])return s?r[o]:r[l.index(d)];for(n in r)if("string"==typeof n&&d.filter(n).add(d.find(n)).length)return r[n]},isProcessing:function(r,o,s){var a=(r=e(r))[0].config,n=s||r.find("."+t.css.header);o?(void 0!==s&&a.sortList.length>0&&(n=n.filter(function(){return!this.sortDisabled&&t.isValueInArray(parseFloat(e(this).attr("data-column")),a.sortList)>=0})),r.add(n).addClass(t.css.processing+" "+a.cssProcessing)):r.add(n).removeClass(t.css.processing+" "+a.cssProcessing)},processTbody:function(t,r,o){if(t=e(t)[0],o)return t.isProcessing=!0,r.before('<colgroup class="tablesorter-savemyplace"/>'),e.fn.detach?r.detach():r.remove();var s=e(t).find("colgroup.tablesorter-savemyplace");r.insertAfter(s),s.remove(),t.isProcessing=!1},clearTableBody:function(t){e(t)[0].config.$tbodies.children().detach()},characterEquivalents:{a:"áàâãäąå",A:"ÁÀÂÃÄĄÅ",c:"çćč",C:"ÇĆČ",e:"éèêëěę",E:"ÉÈÊËĚĘ",i:"íìİîïı",I:"ÍÌİÎÏ",o:"óòôõöō",O:"ÓÒÔÕÖŌ",ss:"ß",SS:"ẞ",u:"úùûüů",U:"ÚÙÛÜŮ"},replaceAccents:function(e){var r,o="[",s=t.characterEquivalents;if(!t.characterRegex){t.characterRegexArray={};for(r in s)"string"==typeof r&&(o+=s[r],t.characterRegexArray[r]=new RegExp("["+s[r]+"]","g"));t.characterRegex=new RegExp(o+"]")}if(t.characterRegex.test(e))for(r in s)"string"==typeof r&&(e=e.replace(t.characterRegexArray[r],r));return e},validateOptions:function(r){var o,s,a,n,i="headers sortForce sortList sortAppend widgets".split(" "),l=r.originalSettings;if(l){t.debug(r,"core")&&(n=new Date);for(o in l)if("undefined"===(a=typeof t.defaults[o]))console.warn('Tablesorter Warning! "table.config.'+o+'" option not recognized');else if("object"===a)for(s in l[o])a=t.defaults[o]&&typeof t.defaults[o][s],e.inArray(o,i)<0&&"undefined"===a&&console.warn('Tablesorter Warning! "table.config.'+o+"."+s+'" option not recognized');t.debug(r,"core")&&console.log("validate options time:"+t.benchmark(n))}},restoreHeaders:function(r){var o,s,a=e(r)[0].config,n=a.$table.find(a.selectorHeaders),i=n.length;for(o=0;o<i;o++)(s=n.eq(o)).find("."+t.css.headerIn).length&&s.html(a.headerContent[o])},destroy:function(r,o,s){if((r=e(r)[0]).hasInitialized){t.removeWidget(r,!0,!1);var a,n=e(r),i=r.config,l=n.find("thead:first"),d=l.find("tr."+t.css.headerRow).removeClass(t.css.headerRow+" "+i.cssHeaderRow),c=n.find("tfoot:first > tr").children("th, td");!1===o&&e.inArray("uitheme",i.widgets)>=0&&(n.triggerHandler("applyWidgetId",["uitheme"]),n.triggerHandler("applyWidgetId",["zebra"])),l.find("tr").not(d).remove(),a="sortReset update updateRows updateAll updateHeaders updateCell addRows updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets removeWidget destroy mouseup mouseleave "+"keypress sortBegin sortEnd resetToLoadState ".split(" ").join(i.namespace+" "),n.removeData("tablesorter").unbind(a.replace(t.regex.spaces," ")),i.$headers.add(c).removeClass([t.css.header,i.cssHeader,i.cssAsc,i.cssDesc,t.css.sortAsc,t.css.sortDesc,t.css.sortNone].join(" ")).removeAttr("data-column").removeAttr("aria-label").attr("aria-disabled","true"),d.find(i.selectorSort).unbind("mousedown mouseup keypress ".split(" ").join(i.namespace+" ").replace(t.regex.spaces," ")),t.restoreHeaders(r),n.toggleClass(t.css.table+" "+i.tableClass+" tablesorter-"+i.theme,!1===o),n.removeClass(i.namespace.slice(1)),r.hasInitialized=!1,delete r.config.cache,"function"==typeof s&&s(r),t.debug(i,"core")&&console.log("tablesorter has been removed")}}};e.fn.tablesorter=function(r){return this.each(function(){var o=this,s=e.extend(!0,{},t.defaults,r,t.instanceMethods);s.originalSettings=r,!o.hasInitialized&&t.buildTable&&"TABLE"!==this.nodeName?t.buildTable(o,s):t.setup(o,s)})},window.console&&window.console.log||(t.logs=[],console={},console.log=console.warn=console.error=console.table=function(){var e=arguments.length>1?arguments:arguments[0];t.logs[t.logs.length]={date:Date.now(),log:e}}),t.addParser({id:"no-parser",is:function(){return!1},format:function(){return""},type:"text"}),t.addParser({id:"text",is:function(){return!0},format:function(r,o){var s=o.config;return r&&(r=e.trim(s.ignoreCase?r.toLocaleLowerCase():r),r=s.sortLocaleCompare?t.replaceAccents(r):r),r},type:"text"}),t.regex.nondigit=/[^\w,. \-()]/g,t.addParser({id:"digit",is:function(e){return t.isDigit(e)},format:function(r,o){var s=t.formatFloat((r||"").replace(t.regex.nondigit,""),o);return r&&"number"==typeof s?s:r?e.trim(r&&o.config.ignoreCase?r.toLocaleLowerCase():r):r},type:"numeric"}),t.regex.currencyReplace=/[+\-,. ]/g,t.regex.currencyTest=/^\(?\d+[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]|[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]\d+\)?$/,t.addParser({id:"currency",is:function(e){return e=(e||"").replace(t.regex.currencyReplace,""),t.regex.currencyTest.test(e)},format:function(r,o){var s=t.formatFloat((r||"").replace(t.regex.nondigit,""),o);return r&&"number"==typeof s?s:r?e.trim(r&&o.config.ignoreCase?r.toLocaleLowerCase():r):r},type:"numeric"}),t.regex.urlProtocolTest=/^(https?|ftp|file):\/\//,t.regex.urlProtocolReplace=/(https?|ftp|file):\/\/(www\.)?/,t.addParser({id:"url",is:function(e){return t.regex.urlProtocolTest.test(e)},format:function(r){return r?e.trim(r.replace(t.regex.urlProtocolReplace,"")):r},type:"text"}),t.regex.dash=/-/g,t.regex.isoDate=/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}/,t.addParser({id:"isoDate",is:function(e){return t.regex.isoDate.test(e)},format:function(e){var r=e?new Date(e.replace(t.regex.dash,"/")):e;return r instanceof Date&&isFinite(r)?r.getTime():e},type:"numeric"}),t.regex.percent=/%/g,t.regex.percentTest=/(\d\s*?%|%\s*?\d)/,t.addParser({id:"percent",is:function(e){return t.regex.percentTest.test(e)&&e.length<15},format:function(e,r){return e?t.formatFloat(e.replace(t.regex.percent,""),r):e},type:"numeric"}),t.addParser({id:"image",is:function(e,t,r,o){return o.find("img").length>0},format:function(t,r,o){return e(o).find("img").attr(r.config.imgAttr||"alt")||t},parsed:!0,type:"text"}),t.regex.dateReplace=/(\S)([AP]M)$/i,t.regex.usLongDateTest1=/^[A-Z]{3,10}\.?\s+\d{1,2},?\s+(\d{4})(\s+\d{1,2}:\d{2}(:\d{2})?(\s+[AP]M)?)?$/i,t.regex.usLongDateTest2=/^\d{1,2}\s+[A-Z]{3,10}\s+\d{4}/i,t.addParser({id:"usLongDate",is:function(e){return t.regex.usLongDateTest1.test(e)||t.regex.usLongDateTest2.test(e)},format:function(e){var r=e?new Date(e.replace(t.regex.dateReplace,"$1 $2")):e;return r instanceof Date&&isFinite(r)?r.getTime():e},type:"numeric"}),t.regex.shortDateTest=/(^\d{1,2}[\/\s]\d{1,2}[\/\s]\d{4})|(^\d{4}[\/\s]\d{1,2}[\/\s]\d{1,2})/,t.regex.shortDateReplace=/[\-.,]/g,t.regex.shortDateXXY=/(\d{1,2})[\/\s](\d{1,2})[\/\s](\d{4})/,t.regex.shortDateYMD=/(\d{4})[\/\s](\d{1,2})[\/\s](\d{1,2})/,t.convertFormat=function(e,r){e=(e||"").replace(t.regex.spaces," ").replace(t.regex.shortDateReplace,"/"),"mmddyyyy"===r?e=e.replace(t.regex.shortDateXXY,"$3/$1/$2"):"ddmmyyyy"===r?e=e.replace(t.regex.shortDateXXY,"$3/$2/$1"):"yyyymmdd"===r&&(e=e.replace(t.regex.shortDateYMD,"$1/$2/$3"));var o=new Date(e);return o instanceof Date&&isFinite(o)?o.getTime():""},t.addParser({id:"shortDate",is:function(e){return e=(e||"").replace(t.regex.spaces," ").replace(t.regex.shortDateReplace,"/"),t.regex.shortDateTest.test(e)},format:function(e,r,o,s){if(e){var a=r.config,n=a.$headerIndexed[s],i=n.length&&n.data("dateFormat")||t.getData(n,t.getColumnData(r,a.headers,s),"dateFormat")||a.dateFormat;return n.length&&n.data("dateFormat",i),t.convertFormat(e,i)||e}return e},type:"numeric"}),t.regex.timeTest=/^(0?[1-9]|1[0-2]):([0-5]\d)(\s[AP]M)$|^((?:[01]\d|[2][0-4]):[0-5]\d)$/i,t.regex.timeMatch=/(0?[1-9]|1[0-2]):([0-5]\d)(\s[AP]M)|((?:[01]\d|[2][0-4]):[0-5]\d)/i,t.addParser({id:"time",is:function(e){return t.regex.timeTest.test(e)},format:function(e){var r,o=(e||"").match(t.regex.timeMatch),s=new Date(e),a=e&&(null!==o?o[0]:"00:00 AM"),n=a?new Date("2000/01/01 "+a.replace(t.regex.dateReplace,"$1 $2")):a;return n instanceof Date&&isFinite(n)?(r=s instanceof Date&&isFinite(s)?s.getTime():0,r?parseFloat(n.getTime()+"."+s.getTime()):n.getTime()):e},type:"numeric"}),t.addParser({id:"metadata",is:function(){return!1},format:function(t,r,o){var s=r.config,a=s.parserMetadataName?s.parserMetadataName:"sortValue";return e(o).metadata()[a]},type:"numeric"}),t.addWidget({id:"zebra",priority:90,format:function(t,r,o){var s,a,n,i,l,d,c,g=new RegExp(r.cssChildRow,"i"),p=r.$tbodies.add(e(r.namespace+"_extra_table").children("tbody:not(."+r.cssInfoBlock+")"));for(l=0;l<p.length;l++)for(n=0,c=(s=p.eq(l).children("tr:visible").not(r.selectorRemove)).length,d=0;d<c;d++)a=s.eq(d),g.test(a[0].className)||n++,i=n%2==0,a.removeClass(o.zebra[i?1:0]).addClass(o.zebra[i?0:1])},remove:function(e,r,o,s){if(!s){var a,n,i=r.$tbodies,l=(o.zebra||["even","odd"]).join(" ");for(a=0;a<i.length;a++)(n=t.processTbody(e,i.eq(a),!0)).children().removeClass(l),t.processTbody(e,n,!1)}}})}(e),e.tablesorter});
</script>
<script type="text/javascript">
/*
    Copyright 2008-2013
        Matthias Ehmann,
        Michael Gerhaeuser,
        Carsten Miller,
        Bianca Valentin,
        Alfred Wassermann,
        Peter Wilfahrt
    Dual licensed under the Apache License Version 2.0, or LGPL Version 3 licenses.
    You should have received a copy of the GNU Lesser General Public License
    along with JSXCompressor.  If not, see <http://www.gnu.org/licenses/>.
    You should have received a copy of the Apache License along with JSXCompressor.
    If not, see <http://www.apache.org/licenses/>.
*/
(function(){var e,r,n;(function(t){function o(e,r){return C.call(e,r)}function i(e,r){var n,t,o,i,a,u,c,f,s,l,p=r&&r.split("/"),h=k.map,d=h&&h["*"]||{};if(e&&"."===e.charAt(0))if(r){for(p=p.slice(0,p.length-1),e=p.concat(e.split("/")),f=0;e.length>f;f+=1)if(l=e[f],"."===l)e.splice(f,1),f-=1;else if(".."===l){if(1===f&&(".."===e[2]||".."===e[0]))break;f>0&&(e.splice(f-1,2),f-=2)}e=e.join("/")}else 0===e.indexOf("./")&&(e=e.substring(2));if((p||d)&&h){for(n=e.split("/"),f=n.length;f>0;f-=1){if(t=n.slice(0,f).join("/"),p)for(s=p.length;s>0;s-=1)if(o=h[p.slice(0,s).join("/")],o&&(o=o[t])){i=o,a=f;break}if(i)break;!u&&d&&d[t]&&(u=d[t],c=f)}!i&&u&&(i=u,a=c),i&&(n.splice(0,a,i),e=n.join("/"))}return e}function a(e,r){return function(){return h.apply(t,v.call(arguments,0).concat([e,r]))}}function u(e){return function(r){return i(r,e)}}function c(e){return function(r){b[e]=r}}function f(e){if(o(m,e)){var r=m[e];delete m[e],y[e]=!0,p.apply(t,r)}if(!o(b,e)&&!o(y,e))throw Error("No "+e);return b[e]}function s(e){var r,n=e?e.indexOf("!"):-1;return n>-1&&(r=e.substring(0,n),e=e.substring(n+1,e.length)),[r,e]}function l(e){return function(){return k&&k.config&&k.config[e]||{}}}var p,h,d,g,b={},m={},k={},y={},C=Object.prototype.hasOwnProperty,v=[].slice;d=function(e,r){var n,t=s(e),o=t[0];return e=t[1],o&&(o=i(o,r),n=f(o)),o?e=n&&n.normalize?n.normalize(e,u(r)):i(e,r):(e=i(e,r),t=s(e),o=t[0],e=t[1],o&&(n=f(o))),{f:o?o+"!"+e:e,n:e,pr:o,p:n}},g={require:function(e){return a(e)},exports:function(e){var r=b[e];return r!==void 0?r:b[e]={}},module:function(e){return{id:e,uri:"",exports:b[e],config:l(e)}}},p=function(e,r,n,i){var u,s,l,p,h,k,C=[];if(i=i||e,"function"==typeof n){for(r=!r.length&&n.length?["require","exports","module"]:r,h=0;r.length>h;h+=1)if(p=d(r[h],i),s=p.f,"require"===s)C[h]=g.require(e);else if("exports"===s)C[h]=g.exports(e),k=!0;else if("module"===s)u=C[h]=g.module(e);else if(o(b,s)||o(m,s)||o(y,s))C[h]=f(s);else{if(!p.p)throw Error(e+" missing "+s);p.p.load(p.n,a(i,!0),c(s),{}),C[h]=b[s]}l=n.apply(b[e],C),e&&(u&&u.exports!==t&&u.exports!==b[e]?b[e]=u.exports:l===t&&k||(b[e]=l))}else e&&(b[e]=n)},e=r=h=function(e,r,n,o,i){return"string"==typeof e?g[e]?g[e](r):f(d(e,r).f):(e.splice||(k=e,r.splice?(e=r,r=n,n=null):e=t),r=r||function(){},"function"==typeof n&&(n=o,o=i),o?p(t,e,r,n):setTimeout(function(){p(t,e,r,n)},4),h)},h.config=function(e){return k=e,k.deps&&h(k.deps,k.callback),h},n=function(e,r,n){r.splice||(n=r,r=[]),o(b,e)||o(m,e)||(m[e]=[e,r,n])},n.amd={jQuery:!0}})(),n("../node_modules/almond/almond",function(){}),n("jxg",[],function(){var e={};return"object"!=typeof JXG||JXG.extend||(e=JXG),e.extend=function(e,r,n,t){var o,i;n=n||!1,t=t||!1;for(o in r)(!n||n&&r.hasOwnProperty(o))&&(i=t?o.toLowerCase():o,e[i]=r[o])},e.extend(e,{boards:{},readers:{},elements:{},registerElement:function(e,r){e=e.toLowerCase(),this.elements[e]=r},registerReader:function(e,r){var n,t;for(n=0;r.length>n;n++)t=r[n].toLowerCase(),"function"!=typeof this.readers[t]&&(this.readers[t]=e)},shortcut:function(e,r){return function(){return e[r].apply(this,arguments)}},getRef:function(e,r){return e.select(r)},getReference:function(e,r){return e.select(r)},debugInt:function(){var e,r;for(e=0;arguments.length>e;e++)r=arguments[e],"object"==typeof window&&window.console&&console.log?console.log(r):"object"==typeof document&&document.getElementById("debug")&&(document.getElementById("debug").innerHTML+=r+"<br/>")},debugWST:function(){var r=Error();e.debugInt.apply(this,arguments),r&&r.stack&&(e.debugInt("stacktrace"),e.debugInt(r.stack.split("\n").slice(1).join("\n")))},debugLine:function(){var r=Error();e.debugInt.apply(this,arguments),r&&r.stack&&e.debugInt("Called from",r.stack.split("\n").slice(2,3).join("\n"))},debug:function(){e.debugInt.apply(this,arguments)}}),e}),n("utils/zip",["jxg"],function(e){var r=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254,1,129,65,193,33,161,97,225,17,145,81,209,49,177,113,241,9,137,73,201,41,169,105,233,25,153,89,217,57,185,121,249,5,133,69,197,37,165,101,229,21,149,85,213,53,181,117,245,13,141,77,205,45,173,109,237,29,157,93,221,61,189,125,253,3,131,67,195,35,163,99,227,19,147,83,211,51,179,115,243,11,139,75,203,43,171,107,235,27,155,91,219,59,187,123,251,7,135,71,199,39,167,103,231,23,151,87,215,55,183,119,247,15,143,79,207,47,175,111,239,31,159,95,223,63,191,127,255],n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],t=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,99,99],o=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],i=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],a=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],u=256;return e.Util=e.Util||{},e.Util.Unzip=function(c){function f(){return R+=8,O>X?c[X++]:-1}function s(){B=1}function l(){var e;try{return R++,e=1&B,B>>=1,0===B&&(B=f(),e=1&B,B=128|B>>1),e}catch(r){throw r}}function p(e){var n=0,t=e;try{for(;t--;)n=n<<1|l();e&&(n=r[n]>>8-e)}catch(o){throw o}return n}function h(){J=0}function d(e){j++,G[J++]=e,z.push(String.fromCharCode(e)),32768===J&&(J=0)}function g(){this.b0=0,this.b1=0,this.jump=null,this.jumppos=-1}function b(){for(;;){if(M[H]>=x)return-1;if(U[M[H]]===H)return M[H]++;M[H]++}}function m(){var e,r=P[F];if(17===H)return-1;if(F++,H++,e=b(),e>=0)r.b0=e;else if(r.b0=32768,m())return-1;if(e=b(),e>=0)r.b1=e,r.jump=null;else if(r.b1=32768,r.jump=P[F],r.jumppos=F,m())return-1;return H--,0}function k(e,r,n){var t;for(P=e,F=0,U=n,x=r,t=0;17>t;t++)M[t]=0;return H=0,m()?-1:0}function y(e){for(var r,n,t,o=0,i=e[o];;)if(t=l()){if(!(32768&i.b1))return i.b1;for(i=i.jump,r=e.length,n=0;r>n;n++)if(e[n]===i){o=n;break}}else{if(!(32768&i.b0))return i.b0;o++,i=e[o]}}function C(){var u,c,b,m,C,v,A,j,w,U,x,S,z,I,E,L,O;do if(u=l(),b=p(2),0===b)for(s(),U=f(),U|=f()<<8,S=f(),S|=f()<<8,65535&(U^~S)&&e.debug("BlockLen checksum mismatch\n");U--;)c=f(),d(c);else if(1===b)for(;;)if(C=r[p(7)]>>1,C>23?(C=C<<1|l(),C>199?(C-=128,C=C<<1|l()):(C-=48,C>143&&(C+=136))):C+=256,256>C)d(C);else{if(256===C)break;for(C-=257,w=p(t[C])+n[C],C=r[p(5)]>>3,i[C]>8?(x=p(8),x|=p(i[C]-8)<<8):x=p(i[C]),x+=o[C],C=0;w>C;C++)c=G[32767&J-x],d(c)}else if(2===b){for(A=Array(320),I=257+p(5),E=1+p(5),L=4+p(4),C=0;19>C;C++)A[C]=0;for(C=0;L>C;C++)A[a[C]]=p(3);for(w=q.length,m=0;w>m;m++)q[m]=new g;if(k(q,19,A,0))return h(),1;for(z=I+E,m=0,O=-1;z>m;)if(O++,C=y(q),16>C)A[m++]=C;else if(16===C){if(C=3+p(2),m+C>z)return h(),1;for(v=m?A[m-1]:0;C--;)A[m++]=v}else{if(C=17===C?3+p(3):11+p(7),m+C>z)return h(),1;for(;C--;)A[m++]=0}for(w=T.length,m=0;w>m;m++)T[m]=new g;if(k(T,I,A,0))return h(),1;for(w=T.length,m=0;w>m;m++)q[m]=new g;for(j=[],m=I;A.length>m;m++)j[m-I]=A[m];if(k(q,E,j,0))return h(),1;for(;;)if(C=y(T),C>=256){if(C-=256,0===C)break;for(C-=1,w=p(t[C])+n[C],C=y(q),i[C]>8?(x=p(8),x|=p(i[C]-8)<<8):x=p(i[C]),x+=o[C];w--;)c=G[32767&J-x],d(c)}else d(C)}while(!u);return h(),s(),0}function v(){var e,r,n,t,o,i,a,c,s=[];try{if(z=[],L=!1,s[0]=f(),s[1]=f(),120===s[0]&&218===s[1]&&(C(),E[I]=[z.join(""),"geonext.gxt"],I++),31===s[0]&&139===s[1]&&(S(),E[I]=[z.join(""),"file"],I++),80===s[0]&&75===s[1]&&(L=!0,s[2]=f(),s[3]=f(),3===s[2]&&4===s[3])){for(s[0]=f(),s[1]=f(),A=f(),A|=f()<<8,c=f(),c|=f()<<8,f(),f(),f(),f(),a=f(),a|=f()<<8,a|=f()<<16,a|=f()<<24,i=f(),i|=f()<<8,i|=f()<<16,i|=f()<<24,o=f(),o|=f()<<8,o|=f()<<16,o|=f()<<24,t=f(),t|=f()<<8,n=f(),n|=f()<<8,e=0,N=[];t--;)r=f(),"/"===r|":"===r?e=0:u-1>e&&(N[e++]=String.fromCharCode(r));for(w||(w=N),e=0;n>e;)r=f(),e++;j=0,8===c&&(C(),E[I]=Array(2),E[I][0]=z.join(""),E[I][1]=N.join(""),I++),S()}}catch(l){throw l}}var A,j,w,U,x,S,z=[],I=0,E=[],G=Array(32768),J=0,L=!1,O=c.length,X=0,B=1,R=0,T=Array(288),q=Array(32),F=0,P=null,H=(Array(64),Array(64),0),M=Array(17),N=[];M[0]=0,S=function(){var e,r,n,t,o,i,a=[];if(8&A&&(a[0]=f(),a[1]=f(),a[2]=f(),a[3]=f(),80===a[0]&&75===a[1]&&7===a[2]&&8===a[3]?(e=f(),e|=f()<<8,e|=f()<<16,e|=f()<<24):e=a[0]|a[1]<<8|a[2]<<16|a[3]<<24,r=f(),r|=f()<<8,r|=f()<<16,r|=f()<<24,n=f(),n|=f()<<8,n|=f()<<16,n|=f()<<24),L&&v(),a[0]=f(),8===a[0]){if(A=f(),f(),f(),f(),f(),f(),t=f(),4&A)for(a[0]=f(),a[2]=f(),H=a[0]+256*a[1],o=0;H>o;o++)f();if(8&A)for(o=0,N=[],i=f();i;)("7"===i||":"===i)&&(o=0),u-1>o&&(N[o++]=i),i=f();if(16&A)for(i=f();i;)i=f();2&A&&(f(),f()),C(),e=f(),e|=f()<<8,e|=f()<<16,e|=f()<<24,n=f(),n|=f()<<8,n|=f()<<16,n|=f()<<24,L&&v()}},e.Util.Unzip.prototype.unzipFile=function(e){var r;for(this.unzip(),r=0;E.length>r;r++)if(E[r][1]===e)return E[r][0];return""},e.Util.Unzip.prototype.unzip=function(){return v(),E}},e.Util}),n("utils/encoding",["jxg"],function(e){var r=0,n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,8,8,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,10,3,3,3,3,3,3,3,3,3,3,3,3,4,3,3,11,6,6,6,5,8,8,8,8,8,8,8,8,8,8,8,0,12,24,36,60,96,84,12,12,12,48,72,12,12,12,12,12,12,12,12,12,12,12,12,12,0,12,12,12,12,12,0,12,0,12,12,12,24,12,12,12,12,12,24,12,24,12,12,12,12,12,12,12,12,12,24,12,12,12,12,12,24,12,12,12,12,12,12,12,24,12,12,12,12,12,12,12,12,12,36,12,36,12,12,12,36,12,12,12,12,12,36,12,36,12,12,12,36,12,12,12,12,12,12,12,12,12,12];return e.Util=e.Util||{},e.Util.UTF8={encode:function(e){var r,n,t="",o=e.length;if(e=e.replace(/\r\n/g,"\n"),"function"==typeof unescape&&"function"==typeof encodeURIComponent)return unescape(encodeURIComponent(e));for(r=0;o>r;r++)n=e.charCodeAt(r),128>n?t+=String.fromCharCode(n):n>127&&2048>n?(t+=String.fromCharCode(192|n>>6),t+=String.fromCharCode(128|63&n)):(t+=String.fromCharCode(224|n>>12),t+=String.fromCharCode(128|63&n>>6),t+=String.fromCharCode(128|63&n));return t},decode:function(e){var t,o,i,a=0,u=0,c=r,f=[],s=e.length,l=[];for(t=0;s>t;t++)o=e.charCodeAt(t),i=n[o],u=c!==r?63&o|u<<6:255>>i&o,c=n[256+c+i],c===r&&(u>65535?f.push(55232+(u>>10),56320+(1023&u)):f.push(u),a++,0===a%1e4&&(l.push(String.fromCharCode.apply(null,f)),f=[]));return l.push(String.fromCharCode.apply(null,f)),l.join("")},asciiCharCodeAt:function(e,r){var n=e.charCodeAt(r);if(n>255)switch(n){case 8364:n=128;break;case 8218:n=130;break;case 402:n=131;break;case 8222:n=132;break;case 8230:n=133;break;case 8224:n=134;break;case 8225:n=135;break;case 710:n=136;break;case 8240:n=137;break;case 352:n=138;break;case 8249:n=139;break;case 338:n=140;break;case 381:n=142;break;case 8216:n=145;break;case 8217:n=146;break;case 8220:n=147;break;case 8221:n=148;break;case 8226:n=149;break;case 8211:n=150;break;case 8212:n=151;break;case 732:n=152;break;case 8482:n=153;break;case 353:n=154;break;case 8250:n=155;break;case 339:n=156;break;case 382:n=158;break;case 376:n=159;break;default:}return n}},e.Util.UTF8}),n("utils/base64",["jxg","utils/encoding"],function(e,r){function n(e,r){return 255&e.charCodeAt(r)}function t(e,r){var n=o.indexOf(e.charAt(r));if(-1===n)throw Error("JSXGraph/utils/base64: Can't decode string (invalid character).");return n}var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i="=";return e.Util=e.Util||{},e.Util.Base64={encode:function(e){var t,a,u,c,f,s=[];for(f=r.encode(e),u=f.length,c=u%3,t=0;u-c>t;t+=3)a=n(f,t)<<16|n(f,t+1)<<8|n(f,t+2),s.push(o.charAt(a>>18),o.charAt(63&a>>12),o.charAt(63&a>>6),o.charAt(63&a));switch(c){case 1:a=n(f,u-1),s.push(o.charAt(a>>2),o.charAt(63&a<<4),i,i);break;case 2:a=n(f,u-2)<<8|n(f,u-1),s.push(o.charAt(a>>10),o.charAt(63&a>>4),o.charAt(63&a<<2),i)}return s.join("")},decode:function(e,n){var o,a,u,c,f,s,l=[],p=[];if(o=e.replace(/[^A-Za-z0-9\+\/=]/g,""),u=o.length,0!==u%4)throw Error("JSXGraph/utils/base64: Can't decode string (invalid input length).");for(o.charAt(u-1)===i&&(c=1,o.charAt(u-2)===i&&(c=2),u-=4),a=0;u>a;a+=4)f=t(o,a)<<18|t(o,a+1)<<12|t(o,a+2)<<6|t(o,a+3),p.push(f>>16,255&f>>8,255&f),0===a%1e4&&(l.push(String.fromCharCode.apply(null,p)),p=[]);switch(c){case 1:f=t(o,u)<<12|t(o,u+1)<<6|t(o,u+2),p.push(f>>10,255&f>>2);break;case 2:f=t(o,a)<<6|t(o,a+1),p.push(f>>4)}return l.push(String.fromCharCode.apply(null,p)),s=l.join(""),n&&(s=r.decode(s)),s},decodeAsArray:function(e){var r,n=this.decode(e),t=[],o=n.length;for(r=0;o>r;r++)t[r]=n.charCodeAt(r);return t}},e.Util.Base64}),n("../build/compressor.deps.js",["jxg","utils/zip","utils/base64"],function(e,r,n){return e.decompress=function(e){return unescape(new r.Unzip(n.decodeAsArray(e)).unzip()[0][0])},e}),window.JXG=r("../build/compressor.deps.js")})();
</script>
<script type="text/javascript">
window.fileLoading = (function () {
    var fileLoadingCallbacks = {};
    var timestamp = new Date().getTime();
    function loadKeywordsFile(filename, callback) {
        fileLoadingCallbacks[filename] = callback;
        var script = document.createElement('script');
        script.type = 'text/javascript';
        // timestamp as an argument to prevent browsers from caching scripts
        // see: http://stackoverflow.com/questions/866619/how-to-force-ie-to-reload-javascript
        script.src = filename+'?time='+timestamp;
        document.getElementsByTagName("head")[0].appendChild(script);
    }
    function getCallbackHandlerForKeywords(parent) {
        var callableList = [];
        return function (callable) {
            if (!parent.isChildrenLoaded) {
                callableList.push(callable);
                if (callableList.length == 1) {
                    loadKeywordsFile(parent.childFileName, function () {
                        parent.isChildrenLoaded = true;
                        for (var i = 0; i < callableList.length; i++) {
                            callableList[i]();
                        }
                    });
                }
            } else {
                callable();
            }
        }
    }
    function notifyFileLoaded(filename) {
        fileLoadingCallbacks[filename]();
    }
    return {
        getCallbackHandlerForKeywords: getCallbackHandlerForKeywords,
        notify: notifyFileLoaded
    }
}());
</script>
<script type="text/javascript">
window.model = (function () {
    function Suite(data) {
        var suite = createModelObject(data);
        suite.source = data.source;
        suite.relativeSource = data.relativeSource;
        suite.fullName = data.parent ? data.parent.fullName + '.' + data.name : data.name;
        setStats(suite, data.statistics);
        suite.metadata = data.metadata;
        suite.populateKeywords = createIterablePopulator('Keyword');
        suite.populateTests = createIterablePopulator('Test');
        suite.populateSuites = createIterablePopulator('Suite');
        suite.childrenNames = ['keyword', 'suite', 'test'];
        suite.callWhenChildrenReady = function (callable) { callable(); };
        suite.message = data.message;
        suite.children = function () {
            return suite.keywords().concat(suite.tests()).concat(suite.suites());
        };
        suite.searchTests = function (predicate) {
            var tests = [];
            var suites = this.suites();
            for (var i in suites)
                tests = tests.concat(suites[i].searchTests(predicate));
            return tests.concat(util.filter(this.tests(), predicate));
        };
        suite.searchTestsInSuite = function (pattern, matcher) {
            if (!matcher)
                matcher = util.Matcher(pattern);
            if (matcher.matchesAny([suite.fullName, suite.name]))
                return suite.allTests();
            var tests = [];
            var suites = this.suites();
            for (var i in suites)
                tests = tests.concat(suites[i].searchTestsInSuite(pattern, matcher));
            return tests;
        };
        suite.searchTestsByTag = function (tag) {
            return suite.searchTests(function (test) {
                if (tag.info == "critical" || tag.info == "non-critical")
                    return containsTagPattern(test.tags, tag.label);
                if (tag.combined)
                    return containsTagPattern(test.tags, tag.combined);
                return containsTag(test.tags, tag.label);
            });
        };
        suite.findSuiteByName = function (name) {
            return findSuiteByName(suite, name);
        };
        suite.allTests = function () {
            return suite.searchTests(function (test) {
                return true;
            });
        };
        suite.criticalTests = function () {
            return suite.searchTests(function (test) {
                return test.isCritical;
            });
        };
        return suite;
    }
    function containsTag(testTags, tagname) {
        testTags = util.map(testTags, util.normalize);
        return util.contains(testTags, util.normalize(tagname));
    }
    function containsTagPattern(testTags, pattern) {
        var patterns;
        if (pattern.indexOf('NOT') != -1) {
            patterns = pattern.split('NOT');
            if (!util.normalize(patterns[0]))
                return util.all(util.map(patterns.slice(1), function (p) {
                    return !containsTagPattern(testTags, p);
                }));
            return containsTagPattern(testTags, patterns[0]) &&
                util.all(util.map(patterns.slice(1), function (p) {
                    return !containsTagPattern(testTags, p);
                }));
        }
        if (pattern.indexOf('OR') != -1) {
            patterns = pattern.split('OR');
            return util.any(util.map(patterns, function (p) {
                return containsTagPattern(testTags, p);
            }));
        }
        if (pattern.indexOf('AND') != -1) {
            patterns = pattern.split('AND');
            return util.all(util.map(patterns, function (p) {
                return containsTagPattern(testTags, p);
            }));
        }
        return util.Matcher(pattern).matchesAny(testTags);
    }
    function findSuiteByName(suite, name) {
        if (suite.fullName == name)
            return suite;
        var subSuites = suite.suites();
        for (var i in subSuites) {
            var match = findSuiteByName(subSuites[i], name);
            if (match)
                return match;
        }
        return null;
    }
    function setStats(suite, stats) {
        for (var name in stats) {
            suite[name] = stats[name];
        }
    }
    function createModelObject(data) {
        return {
            name: data.name,
            doc: data.doc,
            status: data.status,
            times: data.times,
            id: data.parent ? data.parent.id + '-' + data.id : data.id
        };
    }
    function Test(data) {
        var test = createModelObject(data);
        test.fullName = data.parent.fullName + '.' + test.name;
        test.formatParentName = function () { return util.formatParentName(test); };
        test.timeout = data.timeout;
        test.populateKeywords = createIterablePopulator('Keyword');
        test.childrenNames = ['keyword'];
        test.isChildrenLoaded = data.isChildrenLoaded;
        test.callWhenChildrenReady = window.fileLoading.getCallbackHandlerForKeywords(test);
        test.children = function () {
            if (test.isChildrenLoaded)
                return test.keywords();
        };
        test.isCritical = data.isCritical;
        test.tags = data.tags;
        test.message = data.message;
        test.matchesTagPattern = function (pattern) {
            return containsTagPattern(test.tags, pattern);
        };
        test.matchesNamePattern = function (pattern) {
            return util.Matcher(pattern).matchesAny([test.name, test.fullName]);
        };
        return test;
    }
    function Keyword(data) {
        var kw = createModelObject(data);
        kw.libname = data.libname;
        kw.type = data.type;
        kw.arguments = data.args;
        kw.assign = data.assign + (data.assign ? ' =' : '');
        kw.tags = data.tags;
        kw.timeout = data.timeout;
        kw.populateMessages = createIterablePopulator('Message');
        kw.populateKeywords = createIterablePopulator('Keyword');
        kw.childrenNames = ['keyword', 'message'];
        kw.isChildrenLoaded = data.isChildrenLoaded;
        kw.callWhenChildrenReady = window.fileLoading.getCallbackHandlerForKeywords(kw);
        kw.children = function () {
            if (kw.isChildrenLoaded)
                return kw.keywords();
        };
        return kw;
    }
    function Message(level, date, text, link) {
        return {
            level: level,
            time: util.timeFromDate(date),
            date: util.dateFromDate(date),
            text: text,
            link: link
        };
    }
    function Times(timedata) {
        var start = timedata[0];
        var end = timedata[1];
        var elapsed = timedata[2];
        return {
            elapsedMillis: elapsed,
            elapsedTime: util.formatElapsed(elapsed),
            startTime: util.dateTimeFromDate(start),
            endTime:  util.dateTimeFromDate(end)
        };
    }
    function createIterablePopulator(name) {
        return function (populator) {
            populateIterable(this, name, populator);
        };
    }
    function populateIterable(obj, name, populator) {
        name = name.toLowerCase() + 's';
        obj[name] = createGetAllFunction(populator.numberOfItems, populator.creator);
    }
    function createGetAllFunction(numberOfElements, creator) {
        var cached = null;
        return function () {
            if (cached === null) {
                cached = [];
                for (var i = 0; i < numberOfElements(); i++) {
                    cached.push(creator(i));
                }
            }
            return cached;
        };
    }
    return {
        Suite: Suite,
        Test: Test,
        Keyword: Keyword,
        Message: Message,
        Times: Times,
        containsTag: containsTag,  // Exposed for tests
        containsTagPattern: containsTagPattern  // Exposed for tests
    };
}());
window.stats = (function () {
    function Statistics(totalElems, tagElems, suiteElems) {
        return {total: util.map(totalElems, totalStatElem),
                tag:   util.map(tagElems, tagStatElem),
                suite: util.map(suiteElems, suiteStatElem)};
    }
    function statElem(stat) {
        stat.total = stat.pass + stat.fail;
        var percents = calculatePercents(stat.total, stat.pass, stat.fail);
        stat.passPercent = percents[0];
        stat.failPercent = percents[1];
        var widths = calculateWidths(stat.passPercent, stat.failPercent);
        stat.passWidth = widths[0];
        stat.failWidth = widths[1];
        return stat;
    }
    function totalStatElem(data) {
        var stat = statElem(data);
        stat.type = stat.label == 'Critical Tests' ? 'critical' : 'all';
        return stat;
    }
    function tagStatElem(data) {
        var stat = statElem(data);
        stat.links = parseLinks(stat.links);
        return stat;
    }
    function suiteStatElem(data) {
        var stat = statElem(data);
        stat.fullName = stat.label;
        stat.formatParentName = function () { return util.formatParentName(stat); };
        return stat;
    }
    function parseLinks(linksData) {
        if (!linksData)
            return [];
        return util.map(linksData.split(':::'), function (link) {
                var index = link.indexOf(':');
                return {title: link.slice(0, index), url: link.slice(index+1)};
            });
    }
    function calculatePercents(total, passed, failed) {
        if (total == 0)
            return [0.0, 0.0];
        var pass = 100.0 * passed / total;
        var fail = 100.0 * failed / total;
        if (pass > 0 && pass < 0.1)
            return [0.1, 99.9];
        if (fail > 0 && fail < 0.1)
            return [99.9, 0.1];
        return [Math.round(pass*10)/10, Math.round(fail*10)/10];
    }
    function calculateWidths(num1, num2) {
        if (num1 + num2 == 0)
            return [0.0, 0.0];
        // Make small percentages better visible
        if (num1 > 0 && num1 < 1)
            return [1.0, 99.0];
        if (num2 > 0 && num2 < 1)
            return [99.0, 1.0];
        // Handle situation where both are rounded up
        while (num1 + num2 > 100) {
            if (num1 > num2)
                num1 -= 0.1;
            if (num2 > num1)
                num2 -= 0.1;
        }
        return [num1, num2];
    }
    return {
        Statistics: Statistics
    };
}());
</script>
<script type="text/javascript">
window.util = function () {
    function map(elems, func) {
        var ret = [];
        for (var i = 0, len = elems.length; i < len; i++) {
            ret[i] = func(elems[i]);
        }
        return ret;
    }
    function filter(elems, predicate) {
        var ret = [];
        for (var i = 0, len = elems.length; i < len; i++) {
            if (predicate(elems[i]))
                ret.push(elems[i]);
        }
        return ret;
    }
    function all(elems) {
        for (var i = 0, len = elems.length; i < len; i++) {
            if (!elems[i])
                return false;
        }
        return true;
    }
    function any(elems) {
        for (var i = 0, len = elems.length; i < len; i++) {
            if (elems[i])
                return elems[i];
        }
        return false;
    }
    function contains(elems, e) {
        for (var i = 0, len = elems.length; i < len; i++) {
            if (elems[i] == e)
                return true;
        }
        return false;
    }
    function last(items) {
        return items[items.length-1];
    }
    function unescape(string) {
        return string.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
    }
    function escape(string) {
        return string.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    }
    function normalize(string) {
        return string.toLowerCase().replace(/ /g, '').replace(/_/g, '');
    }
    function regexpEscape(string) {
        return string.replace(/[-[\]{}()+?*.,\\^$|#]/g, "\\$&");
    }
    function Matcher(pattern) {
        pattern = regexpEscape(normalize(pattern));
        var rePattern = '^' + pattern.replace(/\\\?/g, '.').replace(/\\\*/g, '[\\s\\S]*') + '$';
        var regexp = new RegExp(rePattern);
        function matches(string) {
            return regexp.test(normalize(string));
        }
        return {
            matches: matches,
            matchesAny: function (strings) {
                for (var i = 0, len = strings.length; i < len; i++)
                    if (matches(strings[i]))
                        return true;
                return false;
            }
        };
    }
    function formatParentName(item) {
        var parentName = item.fullName.slice(0, item.fullName.length - item.name.length);
        return parentName.replace(/\./g, ' . ');
    }
    function timeFromDate(date) {
        if (!date)
            return 'N/A';
        return formatTime(date.getHours(), date.getMinutes(),
                          date.getSeconds(), date.getMilliseconds());
    }
    function dateFromDate(date) {
        if (!date)
            return 'N/A';
        return padTo(date.getFullYear(), 4) +
               padTo(date.getMonth() + 1, 2) +
               padTo(date.getDate(), 2);
    }
    function dateTimeFromDate(date) {
        if (!date)
            return 'N/A';
        return dateFromDate(date) + ' ' + timeFromDate(date);
    }
    function formatTime(hours, minutes, seconds, milliseconds) {
        return padTo(hours, 2) + ':' +
               padTo(minutes, 2) + ':' +
               padTo(seconds, 2) + '.' +
               padTo(milliseconds, 3);
    }
    function formatElapsed(elapsed) {
        var millis = elapsed;
        var hours = Math.floor(millis / (60 * 60 * 1000));
        millis -= hours * 60 * 60 * 1000;
        var minutes = Math.floor(millis / (60 * 1000));
        millis -= minutes * 60 * 1000;
        var seconds = Math.floor(millis / 1000);
        millis -= seconds * 1000;
        return formatTime(hours, minutes, seconds, millis);
    }
    function padTo(number, len) {
        var numString = number + "";
        while (numString.length < len) numString = "0" + numString;
        return numString;
    }
    function timestamp(millis) {
        // used also by tools that do not set window.output.baseMillis
        var base = window.output ? window.output.baseMillis : 0;
        return new Date(base + millis);
    }
    function createGeneratedString(timestamp) {
        var date = new Date(timestamp);
        var dt = dateTimeFromDate(date).slice(0, 17);  // drop millis
        var offset = date.getTimezoneOffset();
        var sign = offset > 0 ? '-' : '+';
        var hh = Math.floor(Math.abs(offset) / 60);
        var mm = Math.abs(offset) % 60;
        return dt + ' UTC' + sign + padTo(hh, 2) + ':' + padTo(mm, 2);
    }
    function createGeneratedAgoString(timestamp) {
        function timeString(time, shortUnit) {
            var unit = {y: 'year', d: 'day', h: 'hour', m: 'minute',
                        s: 'second'}[shortUnit];
            var end = time == 1 ? ' ' : 's ';
            return time + ' ' + unit + end;
        }
        function compensateLeapYears(days, years) {
            // Not a perfect algorithm but ought to be enough
            return days - Math.floor(years / 4);
        }
        var generated = Math.round(timestamp / 1000);
        var current = Math.round(new Date().getTime() / 1000);
        var elapsed = current - generated;
        var prefix = '';
        if (elapsed < 0) {
            prefix = '- ';
            elapsed = Math.abs(elapsed);
        }
        var secs  = elapsed % 60;
        var mins  = Math.floor(elapsed / 60) % 60;
        var hours = Math.floor(elapsed / (60*60)) % 24;
        var days  = Math.floor(elapsed / (60*60*24)) % 365;
        var years = Math.floor(elapsed / (60*60*24*365));
        if (years) {
            days = compensateLeapYears(days, years);
            return prefix + timeString(years, 'y') + timeString(days, 'd');
        } else if (days) {
            return prefix + timeString(days, 'd') + timeString(hours, 'h');
        } else if (hours) {
            return prefix + timeString(hours, 'h') + timeString(mins, 'm');
        } else if (mins) {
            return prefix + timeString(mins, 'm') + timeString(secs, 's');
        } else {
            return prefix + timeString(secs, 's');
        }
    }
    function parseQueryString(query) {
        var result = {};
        if (!query)
            return result;
        var params = query.split('&');
        var parts;
        function decode(item) {
            return decodeURIComponent(item.replace('+', ' '));
        }
        for (var i = 0, len = params.length; i < len; i++) {
            parts = params[i].split('=');
            result[decode(parts.shift())] = decode(parts.join('='));
        }
        return result;
    }
    return {
        map: map,
        filter: filter,
        all: all,
        any: any,
        contains: contains,
        last: last,
        escape: escape,
        unescape: unescape,
        normalize: normalize,
        regexpEscape: regexpEscape,
        Matcher: Matcher,
        formatParentName: formatParentName,
        timeFromDate: timeFromDate,
        dateFromDate: dateFromDate,
        dateTimeFromDate: dateTimeFromDate,
        formatElapsed: formatElapsed,
        timestamp: timestamp,
        createGeneratedString: createGeneratedString,
        createGeneratedAgoString: createGeneratedAgoString,
        parseQueryString: parseQueryString
    };
}();
</script>
<script type="text/javascript">
window.testdata = function () {
    var elementsById = {};
    var idCounter = 0;
    var _statistics = null;
    var LEVELS = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FAIL'];
    var STATUSES = ['FAIL', 'PASS', 'NOT_RUN'];
    var KEYWORDS = ['KEYWORD', 'SETUP', 'TEARDOWN', 'FOR', 'VAR'];
    function addElement(elem) {
        if (!elem.id)
            elem.id = uniqueId();
        elementsById[elem.id] = elem;
        return elem;
    }
    function uniqueId() {
        idCounter++;
        return 'element-id-' + idCounter;
    }
    function times(stats) {
        var startMillis = stats[1];
        var elapsed = stats[2];
        if (startMillis === null)
            return [null, null, elapsed];
        return [util.timestamp(startMillis),
                util.timestamp(startMillis + elapsed),
                elapsed];
    }
    function message(element, strings) {
        return addElement(model.Message(LEVELS[element[1]],
                                        util.timestamp(element[0]),
                                        strings.get(element[2]),
                                        strings.get(element[3])));
    }
    function parseStatus(stats) {
        return STATUSES[stats[0]];
    }
    function childCreator(parent, childType) {
        return function (elem, strings, index) {
            return addElement(childType(parent, elem, strings, index));
        };
    }
    function createKeyword(parent, element, strings, index) {
        var kw = model.Keyword({
            parent: parent,
            type: KEYWORDS[element[0]],
            id: 'k' + (index + 1),
            name: strings.get(element[1]),
            libname: strings.get(element[2]),
            timeout: strings.get(element[3]),
            args: strings.get(element[5]),
            assign: strings.get(element[6]),
            tags: strings.get(element[7]),
            doc: function () {
                var doc = strings.get(element[4]);
                this.doc = function () { return doc; };
                return doc;
            },
            status: parseStatus(element[8], strings),
            times: model.Times(times(element[8])),
            isChildrenLoaded: typeof(element[9]) !== 'number'
        });
        lazyPopulateKeywordsFromFile(kw, element[9], strings);
        kw.populateMessages(Populator(element[10], strings, message));
        return kw;
    }
    function lazyPopulateKeywordsFromFile(parent, modelOrIndex, strings) {
        var model, index, populator;
        var creator = childCreator(parent, createKeyword);
        if (parent.isChildrenLoaded) {
            model = modelOrIndex;
            populator = Populator(model, strings, creator);
        } else {
            index = modelOrIndex;
            parent.childFileName = window.settings['splitLogBase'] + '-' + index + '.js';
            populator = SplitLogPopulator(index, creator);
        }
        parent.populateKeywords(populator);
    }
    function tags(taglist, strings) {
        return util.map(taglist, strings.get);
    }
    function createTest(parent, element, strings, index) {
        var statusElement = element[5];
        var test = model.Test({
            parent: parent,
            id: 't' + (index + 1),
            name: strings.get(element[0]),
            doc: function () {
                var doc = strings.get(element[3]);
                this.doc = function () { return doc; };
                return doc;
            },
            timeout: strings.get(element[1]),
            isCritical: element[2],
            status: parseStatus(statusElement),
            message: function () {
                var msg = createMessage(statusElement, strings);
                this.message = function () { return msg; };
                return msg;
            },
            times: model.Times(times(statusElement)),
            tags: tags(element[4], strings),
            isChildrenLoaded: typeof(element[6]) !== 'number'
        });
        lazyPopulateKeywordsFromFile(test, element[6], strings);
        return test;
    }
    function createMessage(statusElement, strings) {
        return statusElement.length == 4 ? strings.get(statusElement[3]) : '';
    }
    function createSuite(parent, element, strings, index) {
        var statusElement = element[5];
        var suite = model.Suite({
            parent: parent,
            id: 's' + ((index || 0) + 1),
            name: strings.get(element[0]),
            source: strings.get(element[1]),
            relativeSource: strings.get(element[2]),
            doc: function () {
                var doc = strings.get(element[3]);
                this.doc = function () { return doc; };
                return doc;
            },
            status: parseStatus(statusElement),
            message: function () {
                var msg = createMessage(statusElement, strings);
                this.message = function () { return msg; };
                return msg;
            },
            times: model.Times(times(statusElement)),
            statistics: suiteStats(util.last(element)),
            metadata: parseMetadata(element[4], strings)
        });
        suite.populateKeywords(Populator(element[8], strings, childCreator(suite, createKeyword)));
        suite.populateTests(Populator(element[7], strings, childCreator(suite, createTest)));
        suite.populateSuites(Populator(element[6], strings, childCreator(suite, createSuite)));
        return suite;
    }
    function parseMetadata(data, strings) {
        var metadata = [];
        for (var i=0; i<data.length; i+=2) {
            metadata.push([strings.get(data[i]), strings.get(data[i+1])]);
        }
        return metadata;
    }
    function suiteStats(stats) {
        return {
            total: stats[0],
            totalPassed: stats[1],
            totalFailed: stats[0] - stats[1],
            critical: stats[2],
            criticalPassed: stats[3],
            criticalFailed: stats[2] - stats[3]
        };
    }
    function Populator(items, strings, creator) {
        return {
            numberOfItems: function () {
                return items.length;
            },
            creator: function (index) {
                return creator(items[index], strings, index);
            }
        };
    }
    function SplitLogPopulator(structureIndex, creator) {
        return {
            numberOfItems: function () {
                return window['keywords'+structureIndex].length;
            },
            creator: function (index) {
                return creator(window['keywords'+structureIndex][index],
                               StringStore(window['strings'+structureIndex]),
                               index);
            }
        };
    }
    function suite() {
        var elem = window.output.suite;
        if (elementsById[elem.id])
            return elem;
        var root = addElement(createSuite(null, elem, StringStore(window.output.strings)));
        window.output.suite = root;
        return root;
    }
    function findLoaded(id) {
        return elementsById[id];
    }
    function ensureLoaded(id, callback) {
        var ids = id.split('-');
        var root = suite();
        ids.shift();
        loadItems(ids, root, [root.id], callback);
    }
    function loadItems(ids, current, result, callback) {
        if (!ids.length) {
            callback(result);
            return;
        }
        current.callWhenChildrenReady(function () {
            var id = ids.shift();
            var type = id[0];
            var index = parseInt(id.substring(1)) - 1;
            var item = selectFrom(current, type, index);
            if (item)
                result.push(item.id);
            else    // Invalid id. Should this be reported somewhere?
                ids = [];
            loadItems(ids, item, result, callback);
        });
    }
    function selectFrom(element, type, index) {
        if (type === 'k') {
            return element.keywords()[index];
        } else if (type === 't') {
            return element.tests()[index];
        } else {
            return element.suites()[index];
        }
    }
    function errorIterator() {
        return {
            next: function () {
                return message(window.output.errors.shift(),
                               StringStore(window.output.strings));
            },
            hasNext: function () {
                return window.output.errors.length > 0;
            }
        };
    }
    function statistics() {
        if (!_statistics) {
            var statData = window.output.stats;
            _statistics = stats.Statistics(statData[0], statData[1], statData[2]);
        }
        return _statistics;
    }
    function StringStore(strings) {
        function getText(id) {
            var text = strings[id];
            if (!text)
                return '';
            if (text[0] == '*')
                return text.substring(1);
            var extracted = extract(text);
            strings[id] = '*' + extracted;
            return extracted;
        }
        function extract(text) {
            var decoded = JXG.Util.Base64.decodeAsArray(text);
            var extracted = (new JXG.Util.Unzip(decoded)).unzip()[0][0];
            return JXG.Util.UTF8.decode(extracted);
        }
        function get(id) {
            if (id === null) return null;
            return getText(id);
        }
        return {get: get};
    }
    return {
        suite: suite,
        errorIterator: errorIterator,
        findLoaded: findLoaded,
        ensureLoaded: ensureLoaded,
        statistics: statistics,
        StringStore: StringStore,  // exposed for tests
        LEVELS: LEVELS
    };
}();
</script>
<script type="text/javascript">
function removeJavaScriptDisabledWarning() {
    // Not using jQuery here for maximum speed
    document.getElementById('javascript-disabled').style.display = 'none';
}
function addJavaScriptDisabledWarning(error) {
    if (window.console)
        console.error('Opening failed: ' + error.name + ': ' + error.message);
    document.getElementById('javascript-disabled').style.display = 'block';
}
function initLayout(suiteName, type) {
    parseTemplates();
    setTitle(suiteName, type);
    addHeader();
    addReportOrLogLink(type);
}
function parseTemplates() {
    $('script[type="text/x-jquery-tmpl"]').map(function (idx, elem) {
        $.template(elem.id, elem.text);
    });
}
function testOrTask(text) {
    return text.replace(/{(.*)}/, function (match, group, offset, string) {
        if (!window.settings.rpa)
            return group;
        return {'TEST': 'TASK', 'Test': 'Task', 'test': 'task'}[group];
    });
}
function setTitle(suiteName, type) {
    var givenTitle = window.settings.title;
    var title = givenTitle ? givenTitle : suiteName + " " + type;
    document.title = util.unescape(title);
}
function addHeader() {
    var generated = util.timestamp(window.output.generated);
    $.tmpl('<h1>${title}</h1>' +
           '<div id="generated">' +
             '<span>Generated<br>${generated}</span><br>' +
             '<span id="generated-ago">${ago} ago</span>' +
           '</div>' +
           '<div id="top-right-header">' +
             '<div id="report-or-log-link"><a href="#"></a></div>' +
           '</div>', {
        generated: util.createGeneratedString(generated),
        ago: util.createGeneratedAgoString(generated),
        title: document.title
    }).appendTo($('#header'));
}
function addReportOrLogLink(myType) {
    var url;
    var text;
    var container = $('#report-or-log-link');
    if (myType == 'Report') {
        url = window.settings.logURL;
        text = 'LOG';
    } else {
        url = window.settings.reportURL;
        text = 'REPORT';
    }
    if (url) {
        container.find('a').attr('href', url);
        container.find('a').text(text);
    } else {
        container.remove();
    }
}
function addStatistics() {
    var statHeaders =
        '<th class="stats-col-stat">Total</th>' +
        '<th class="stats-col-stat">Pass</th>' +
        '<th class="stats-col-stat">Fail</th>' +
        '<th class="stats-col-elapsed">Elapsed</th>' +
        '<th class="stats-col-graph">Pass / Fail</th>';
    var statTable =
        '<h2>{Test} Statistics</h2>' +
        '<table class="statistics" id="total-stats"><thead><tr>' +
        '<th class="stats-col-name">Total Statistics</th>' + statHeaders +
        '</tr></thead></table>' +
        '<table class="statistics" id="tag-stats"><thead><tr>' +
        '<th class="stats-col-name">Statistics by Tag</th>' + statHeaders +
        '</tr></thead></table>' +
        '<table class="statistics" id="suite-stats"><thead><tr>' +
        '<th class="stats-col-name">Statistics by Suite</th>' + statHeaders +
        '</tr></thead></table>';
    $(testOrTask(statTable)).appendTo('#statistics-container');
    util.map(['total', 'tag', 'suite'], addStatTable);
    addTooltipsToElapsedTimes();
    enableStatisticsSorter();
}
function addTooltipsToElapsedTimes() {
    $('.stats-col-elapsed').attr('title',
        testOrTask('Total execution time of these {test}s. ') +
        'Excludes suite setups and teardowns.');
    $('#suite-stats').find('.stats-col-elapsed').attr('title',
        'Total execution time of this suite.');
}
function enableStatisticsSorter() {
    $.tablesorter.addParser({
        id: 'statName',
        type: 'numeric',
        is: function(s) {
            return false;  // do not auto-detect
        },
        format: function(string, table, cell, cellIndex) {
            // Rows have class in format 'row-<index>'.
            var index = $(cell).parent().attr('class').substring(4);
            return parseInt(index);
        }
    });
    $(".statistics").tablesorter({
        sortInitialOrder: 'desc',
        headers: {0: {sorter:'statName', sortInitialOrder: 'asc'},
                  5: {sorter: false}}
    });
}
function addStatTable(tableName) {
    var stats = window.testdata.statistics()[tableName];
    if (tableName == 'tag' && stats.length == 0) {
        renderNoTagStatTable();
    } else {
        renderStatTable(tableName, stats);
    }
}
function renderNoTagStatTable() {
    $('<tbody><tr class="row-0">' +
        '<td class="stats-col-name">No Tags</td>' +
        '<td class="stats-col-stat"></td>' +
        '<td class="stats-col-stat"></td>' +
        '<td class="stats-col-stat"></td>' +
        '<td class="stats-col-elapsed"></td>' +
        '<td class="stats-col-graph">' +
          '<div class="empty-graph"></div>' +
        '</td>' +
      '</tr></tbody>').appendTo('#tag-stats');
}
function renderStatTable(tableName, stats) {
    var template = tableName + 'StatisticsRowTemplate';
    var tbody = $('<tbody></tbody>');
    for (var i = 0, len = stats.length; i < len; i++) {
        $.tmpl(template, stats[i], {index: i}).appendTo(tbody);
    }
    tbody.appendTo('#' + tableName + '-stats');
}
$.template('statColumnsTemplate',
    '<td class="stats-col-stat">${total}</td>' +
    '<td class="stats-col-stat">${pass}</td>' +
    '<td class="stats-col-stat">${fail}</td>' +
    '<td class="stats-col-elapsed">${elapsed}</td>' +
    '<td class="stats-col-graph">' +
      '{{if total}}' +
      '<div class="graph">' +
        '<div class="pass-bar" style="width: ${passWidth}%" title="${passPercent}%"></div>' +
        '<div class="fail-bar" style="width: ${failWidth}%" title="${failPercent}%"></div>' +
      '</div>' +
      '{{else}}' +
      '<div class="empty-graph"></div>' +
      '{{/if}}' +
    '</td>'
);
$.template('suiteStatusMessageTemplate',
    '${critical} critical {{= testOrTask("{test}")}}, ' +
    '${criticalPassed} passed, ' +
    '<span class="{{if criticalFailed}}fail{{else}}pass{{/if}}">${criticalFailed} failed</span><br>' +
    '${total} {{= testOrTask("{test}")}} total, ' +
    '${totalPassed} passed, ' +
    '<span class="{{if totalFailed}}fail{{else}}pass{{/if}}">${totalFailed} failed</span>'
);
// For complete cross-browser experience..
// http://www.quirksmode.org/js/events_order.html
function stopPropagation(event) {
    var event = event || window.event;
    event.cancelBubble = true;
    if (event.stopPropagation)
        event.stopPropagation();
}
</script>
<script type="text/javascript">
storage = function () {
    var prefix = 'robot-framework-';
    var storage;
    function init(user) {
        prefix += user + '-';
        storage = getStorage();
    }
    function getStorage() {
        // Use localStorage if it's accessible, normal object otherwise.
        // Inspired by https://stackoverflow.com/questions/11214404
        try {
            localStorage.setItem(prefix, prefix);
            localStorage.removeItem(prefix);
            return localStorage;
        } catch (exception) {
            return {};
        }
    }
    function get(name, defaultValue) {
        var value = storage[prefix + name];
        if (typeof value === 'undefined')
            return defaultValue;
        return value;
    }
    function set(name, value) {
        storage[prefix + name] = value;
    }
    return {init: init, get: get, set: set};
}();
</script>
<script type="text/javascript">
window.output = {};
</script>
<script type="text/javascript">
window.output["suite"] = [1,2,0,0,[],[1,null,374396],[[3,4,0,0,[],[1,null,374396],[[5,6,0,0,[],[1,null,374396],[[7,8,0,0,[],[1,null,368750],[[9,10,0,0,[],[1,null,194811],[[11,12,0,0,[],[1,null,194811],[[13,14,0,0,[],[1,null,85426],[],[[15,0,1,16,[],[1,0,85426],[]]],[],[1,1,1,1]],[17,18,0,0,[],[1,null,109385],[],[[19,20,1,21,[],[1,312277,27285],[]],[22,20,1,23,[],[1,339563,82100],[]]],[],[2,2,2,2]]],[],[],[3,3,3,3]]],[],[],[3,3,3,3]]],[],[],[3,3,3,3]]],[],[],[3,3,3,3]]],[],[],[3,3,3,3]]],[],[],[3,3,3,3]];
</script>
<script type="text/javascript">
window.output["strings"] = [];
</script>
<script type="text/javascript">
window.output["strings"] = window.output["strings"].concat(["*","*V4Code","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code","*Autobmcv4","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4","*SYSTEST","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST","*BMC V4","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST\\BMC_V4","*厂商定制化用例","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST\\BMC_V4\\厂商定制化用例","*京东","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST\\BMC_V4\\厂商定制化用例\\京东","*京东 1","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST\\BMC_V4\\厂商定制化用例\\京东\\京东_1.robot","*SAC-1309237 BMCv4-REDFISH-TREE-JD-009 修改指定用户信息","*<p>用例等级 : 一般\x3c/p>\n<p>描述 :\x3c/p>\n<p>预置条件 : customID为京东\x3c/p>\n<p>是否需要自动化 : 是\x3c/p>\n<p>用例类型 : 10-基本功能\x3c/p>\n<p>作者 : 钟宏民********\x3c/p>\n<p>其它说明 :\x3c/p>\n<p>步骤 : 1\x3c/p>\n<p>操作 : PATCH请求，url：https://BMC_IP/redfish/v1/AccountService/Accounts/account_id，请求头：X-Auth-Token:auth_value；If-Match: ETag_value；Content-Type:header_type，请求消息体：{ \"UserName\":\"name\", \"Password\":\"password\", \"Enabled\": true, \"RoleId\":\"Administrator\", }\x3c/p>\n<p>预期结果 : UserName:string 用户名 Password:密码 显示为null Enabled: true or false RoleId:string 用户权限,包括Administrator,Operator,User。\"Oem\":{ \"JD\":{ \"Status\":0 } }\x3c/p>\n<p>步骤 : 2\x3c/p>\n<p>操作 : 针对厂商定制化版本发布测试：每步预期结果进行截图，测试内容红框框出\x3c/p>\n<p>预期结果 : 已附件的形式贴到测试报告表格中，如超过2张图片，打包后上传\x3c/p>\n<p>自动化编写日期 : 2024-08-05\x3c/p>\n<p>自动化作者 : 邓雨鑫\x3c/p>\n<p>是否实现自动化 : 是\x3c/p>\n<p>自动化备注 :\x3c/p>","*京东 2","*C:\\jenkins_build\\workspace\\DailyCI\\V4Code\\autobmcv4\\SYSTEST\\BMC_V4\\厂商定制化用例\\京东\\京东_2.robot","*SAC-1309260 BMCv4-REDFISH-TREE-JD-025 查询BMC时区","*30 minutes","*<p>用例等级 : 一般\x3c/p>\n<p>描述 :\x3c/p>\n<p>预置条件 : customID为京东\x3c/p>\n<p>是否需要自动化 : 是\x3c/p>\n<p>用例类型 : 10-基本功能\x3c/p>\n<p>作者 : 钟宏民********\x3c/p>\n<p>其它说明 :\x3c/p>\n<p>步骤 : 1\x3c/p>\n<p>操作 : GET请求，url：https://BMC_IP/redfish/v1/Managers/{Manager_id}，请求头：X-Auth-Token:auth_value，请求消息体：null\x3c/p>\n<p>预期结果 : \"DateTime\": \"2023-05-08T02:10:22+08:00\", \"DateTimeLocalOffset\": \"+08:00\",\x3c/p>\n<p>步骤 : 2\x3c/p>\n<p>操作 : 针对厂商定制化版本发布测试：每步预期结果进行截图，测试内容红框框出\x3c/p>\n<p>预期结果 : 已附件的形式贴到测试报告表格中，如超过2张图片，打包后上传\x3c/p>\n<p>自动化编写日期 : 2024-08-08\x3c/p>\n<p>自动化作者 : 童代云\x3c/p>\n<p>是否实现自动化 : 是\x3c/p>\n<p>自动化备注 :\x3c/p>","*SAC-1309275 BMCv4-REDFISH-TREE-JD-042 查询存储卡详细信息","*<p>用例等级 : 一般\x3c/p>\n<p>描述 :\x3c/p>\n<p>预置条件 : customID为京东\x3c/p>\n<p>是否需要自动化 : 是\x3c/p>\n<p>用例类型 : 10-基本功能\x3c/p>\n<p>作者 : 钟宏民********\x3c/p>\n<p>其它说明 :\x3c/p>\n<p>步骤 : 1\x3c/p>\n<p>操作 : GET请求，url：https://device_ip/redfish/v1/Systems/System_id/Storage/storage_id，请求头：X-Auth-Token:auth_value，请求消息体：null\x3c/p>\n<p>预期结果 : Name :string Resource名称,此处固定为StorageAdapterX State : string Enabled/Disabled Type: string Raid/HBA Adapter类型 FirmwareVersion:string FW信息 Manufacturer: string 厂商信息 Model:string 型号 \"SpeedGbps\": 6,12,24 - 校验类型为数字 PartNumber:string PN信息/Model number SerialNumber:string SN信息 \"TotalCacheSizeMiB\": number Raid卡Cache大小,如无Cache则显示0 \"CapacitanceModel\": string, 超级电容型号,无则显示null - 校验存在此字段 \"CapacitanceStatus\":string OK/Error超级电容健康状态,无则显示null - 校验结果应为 OK或null \"Status\": { \"Health\": \"OK\", \"State\": \"Enabled\" }\x3c/p>\n<p>步骤 : 2\x3c/p>\n<p>操作 : 针对厂商定制化版本发布测试：每步预期结果进行截图，测试内容红框框出\x3c/p>\n<p>预期结果 : 已附件的形式贴到测试报告表格中，如超过2张图片，打包后上传\x3c/p>\n<p>自动化编写日期 :2024-09-03\x3c/p>\n<p>自动化作者 : 徐家奎\x3c/p>\n<p>是否实现自动化 : 是\x3c/p>\n<p>自动化备注 :\x3c/p>"]);
</script>
<script type="text/javascript">
window.output["stats"] = [[{"elapsed":"00:03:15","fail":0,"label":"Critical Tests","pass":3},{"elapsed":"00:03:15","fail":0,"label":"All Tests","pass":3}],[],[{"elapsed":"00:06:14","fail":0,"id":"s1","label":"V4Code","name":"V4Code","pass":3},{"elapsed":"00:06:14","fail":0,"id":"s1-s1","label":"V4Code.Autobmcv4","name":"Autobmcv4","pass":3},{"elapsed":"00:06:14","fail":0,"id":"s1-s1-s1","label":"V4Code.Autobmcv4.SYSTEST","name":"SYSTEST","pass":3},{"elapsed":"00:06:09","fail":0,"id":"s1-s1-s1-s1","label":"V4Code.Autobmcv4.SYSTEST.BMC V4","name":"BMC V4","pass":3},{"elapsed":"00:03:15","fail":0,"id":"s1-s1-s1-s1-s1","label":"V4Code.Autobmcv4.SYSTEST.BMC V4.厂商定制化用例","name":"厂商定制化用例","pass":3},{"elapsed":"00:03:15","fail":0,"id":"s1-s1-s1-s1-s1-s1","label":"V4Code.Autobmcv4.SYSTEST.BMC V4.厂商定制化用例.京东","name":"京东","pass":3},{"elapsed":"00:01:25","fail":0,"id":"s1-s1-s1-s1-s1-s1-s1","label":"V4Code.Autobmcv4.SYSTEST.BMC V4.厂商定制化用例.京东.京东 1","name":"京东 1","pass":1},{"elapsed":"00:01:49","fail":0,"id":"s1-s1-s1-s1-s1-s1-s2","label":"V4Code.Autobmcv4.SYSTEST.BMC V4.厂商定制化用例.京东.京东 2","name":"京东 2","pass":2}]];
</script>
<script type="text/javascript">
window.output["baseMillis"] = 1742636475994;
</script>
<script type="text/javascript">
window.output["generated"] = 2828550495;
</script>
<script type="text/javascript">
window.settings = {"background":{"fail":"#f66","nonCriticalFail":"#9e9","pass":"#9e9"},"logURL":"log.html","rpa":false,"title":""};
</script>
<title></title>
</head>
<body>
<div id="javascript-disabled">
  <h1>Opening Robot Framework report failed</h1>
  <ul>
    <li>Verify that you have <b>JavaScript enabled</b> in your browser.</li>
    <li>Make sure you are using a <b>modern enough browser</b>. If using Internet Explorer, version 8 or newer is required.</li>
    <li>Check are there messages in your browser's <b>JavaScript error log</b>. Please report the problem if you suspect you have encountered a bug.</li>
  </ul>
</div>
<script type="text/javascript">removeJavaScriptDisabledWarning();</script>

<div id="header"></div>
<div id="statistics-container"></div>
<div id="test-details-container"></div>

<script type="text/javascript">
$(document).ready(function () {
    try {
        var topsuite = window.testdata.suite();
    } catch (error) {
        addJavaScriptDisabledWarning(error);
        return;
    }
    window.prevLocationHash = '';
    setBackground(topsuite);
    initLayout(topsuite.name, 'Report');
    storage.init('report');
    addSummary(topsuite);
    addStatistics();
    addDetails();
    window.onhashchange = showDetailsByHash;
});

function setBackground(topsuite) {
    var color;
    if (topsuite.criticalFailed)
        color = window.settings.background.fail;
    else if (topsuite.totalFailed)
        color = window.settings.background.nonCriticalFail;
    else
        color = window.settings.background.pass;
    $('body').css('background-color', color);
}

function addSummary(topsuite) {
    var opts = {logURL: window.settings.logURL};
    $.tmpl('summaryTableTemplate', topsuite, opts).insertAfter($('#header'));
}

function addDetails() {
    addCustomSortersForDetails();
    if (window.location.hash)
        showDetailsByHash();
    else
        renderTotalSelector();
}

function addCustomSortersForDetails() {
    $.tablesorter.addParser({
        id: 'criticality',
        type: 'numeric',
        is: function(s) {
            return false;  // do not auto-detect
        },
        format: function(s) {
            return s === 'yes' ? 0 : 1;
        }
    });
    $.tablesorter.addParser({
        id: 'times',
        type: 'text',
        is: function(s) {
            return false;  // do not auto-detect
        },
        format: function(s) {
            return s.substring(0, 21);  // return only start time
        }
    });
}

function showDetailsByHash() {
    // Cannot use window.location.hash because Firefox incorrectly decodes it:
    // http://stackoverflow.com/questions/1703552/encoding-of-window-location-hash
    var hash = window.location.href.split('#').slice(1).join('#');
    if (!hash || hash == window.prevLocationHash)
        return;
    var parts = hash.split('?');
    var name = parts.shift();
    var query = parts.join('?');
    if (name == 'search') {
        var params = util.parseQueryString(query);
        searchExecuted(params.suite || '', params.test || '',
                       params.include || '', params.exclude || '');
        return;
    }
    query = decodeURIComponent(query);
    var action = {'totals': totalDetailSelected,
                  'tags':   tagDetailSelected,
                  'suites': suiteDetailSelected}[name];
    if (action)
        action(query);
}

function totalDetailSelected(name) {
    renderTotalSelector(name);
    if (name) {
        renderTotalDetails(name);
        var title = name == 'critical' ? 'Critical {Test}s' : 'All {Test}s'
        updatePrintSelector(testOrTask(title));
    }
    scrollToSelector('totals', name);
}

function renderTotalSelector(name) {
    var args = {linkTarget: (name) ? 'totals?'+name : 'totals',
                totalTabStatus: 'detail-tab-selected'};
    renderSelector(args, 'totalDetailsSelectorTemplate', {selected: name});
}

function renderTotalDetails(name) {
    var index = (name == 'critical') ? 0 : 1;
    var stat = window.testdata.statistics().total[index];
    var tests = getTotalTests(name);
    stat.totalTime = calculateTotalTime(tests);
    $.tmpl('tagOrTotalDetailsTemplate', stat).appendTo('#details-header');
    drawTestDetailsTable(tests, true);
}

function updatePrintSelector(name, info) {
    if (info)
        name += ' (' + info + ')';
    $('#print-selector').html(name);
}

function tagDetailSelected(name) {
    renderTagSelector(name);
    if (name) {
        var tag = findTag(name);
        if (tag) {
            renderTagDetails(tag);
            updatePrintSelector(name, tag.info);
        }
    }
    scrollToSelector('tags', name);
}

function findTag(name) {
    var tags = window.testdata.statistics().tag;
    for (var i = 0, len = tags.length; i < len; i++) {
        if (tags[i].label == name)
            return tags[i];
    }
    return null;
}

function renderTagSelector(name) {
    var args = {linkTarget: (name) ? 'tags?'+name : 'tags',
                tagTabStatus: 'detail-tab-selected'};
    var stats = {tags: window.testdata.statistics().tag, selected: name};
    renderSelector(args, 'tagDetailsSelectorTemplate', stats);
}

function renderTagDetails(tag) {
    var tests = getTestsHavingTag(tag);
    tag.totalTime = calculateTotalTime(tests);
    $.tmpl('tagOrTotalDetailsTemplate', tag).appendTo('#details-header');
    drawTestDetailsTable(tests, true);
}

function suiteDetailSelected(id) {
    renderSuiteSelector(id);
    if (id)
        renderSuiteDetails(id);
    scrollToSelector('suites', id);
}

function renderSuiteSelector(id) {
    var args = {linkTarget: (id) ? 'suites?'+id : 'suites',
                suiteTabStatus: 'detail-tab-selected'};
    var stats = {suites: window.testdata.statistics().suite,
                 selected: id};
    renderSelector(args, 'suiteDetailsSelectorTemplate', stats);
}

function renderSuiteDetails(id) {
    window.testdata.ensureLoaded(id, function (ids) {
        var suite = window.testdata.findLoaded(id);
        var opts = {logURL: window.settings.logURL};
        $.tmpl('suiteDetailsTemplate', suite, opts).appendTo('#details-header');
        drawTestDetailsTable(suite.allTests(), false);
        updatePrintSelector(suite.fullName);
    });
}

function searchExecuted(suite, test, include, exclude) {
    renderSearchSelector(suite, test, include, exclude);
    if (suite || test || include || exclude) {
        renderSearchDetails(suite, test, include, exclude);
        scrollToSelector('search' +
                         '?suite=' + encodeURIComponent(suite) +
                         '&test=' + encodeURIComponent(test) +
                         '&include=' + encodeURIComponent(include) +
                         '&exclude=' + encodeURIComponent(exclude));
    } else {
        scrollToSelector('search');
    }
}

function renderSearchSelector(suite, test, include, exclude) {
    var args = {linkTarget: (suite || test || include || exclude) ?
                            ('search?suite=' + suite + '&test=' + test + '&include=' + include + '&exclude=' + exclude) :
                            'search',
                searchTabStatus: 'detail-tab-selected'};
    var search = {suite: suite, test: test, include: include, exclude: exclude};
    renderSelector(args, 'searchSelectorTemplate', search);
}

function renderSearchDetails(suite, test, include, exclude) {
    var tests = searchTests(util.escape(suite), util.escape(test),
                            util.escape(include), util.escape(exclude));
    var passed = calculatePassed(tests);
    var stats = {total: tests.length,
                 pass: passed,
                 fail: tests.length - passed,
                 totalTime: calculateTotalTime(tests)};
    $.tmpl('tagOrTotalDetailsTemplate', stats).appendTo('#details-header');
    drawTestDetailsTable(tests, true);
}

function searchTests(suitePattern, testPattern, includePattern, excludePattern) {
    var tests;
    if (suitePattern)
        tests = window.testdata.suite().searchTestsInSuite(suitePattern);
    else
        tests = window.testdata.suite().allTests();
    return util.filter(tests, function (test) {
        if (testPattern && !test.matchesNamePattern(testPattern))
            return false;
        if (includePattern && !test.matchesTagPattern(includePattern))
            return false;
        return !(excludePattern && test.matchesTagPattern(excludePattern));
    });
}

function scrollToSelector(base, query) {
    $('#test-details-container').css('min-height', $(window).height());
    var anchor = query ? base + '?' + encodeURIComponent(query) : base;
    window.location.hash = '';
    window.location.hash = window.prevLocationHash = anchor;
}

function renderSelector(args, template, stats) {
    window.elementsToRender = [];
    var container = $('#test-details-container');
    container.empty();
    $.tmpl('detailsHeaderTemplate', args).appendTo(container);
    $.tmpl(template, stats).appendTo(container);
}

function drawTestDetailsTable(tests, sortByStatus) {
    if (!tests.length)
        return;
    renderTestDetailsHeader();
    window.elementsToRender = tests;
    var target = $('#test-details').find('tbody');
    renderTestDetails(sortByStatus, target);
}

function renderTestDetailsHeader() {
    var header = $.tmpl('testDetailsTableTemplate');
    hideHiddenDetailsColumns(header);
    header.appendTo('#test-details-container');
}

function sortByStatus(t1, t2) {
    if (t1.status != t2.status)
        return t1.status == 'FAIL' ? -1 : 1;
    if (t1.isCritical != t2.isCritical)
        return t1.isCritical ? -1 : 1;
    return t1.fullName < t2.fullName ? -1 : 1;
}

function getTestsHavingTag(tag) {
    return window.testdata.suite().searchTestsByTag(tag).sort(sortByStatus);
}

function getTotalTests(name) {
    if (name == 'critical')
        return window.testdata.suite().criticalTests().sort(sortByStatus);
    return window.testdata.suite().allTests().sort(sortByStatus);
}

function calculateTotalTime(tests) {
    var total = 0;
    for (var i = 0, len = tests.length; i < len; i++)
        total += tests[i].times.elapsedMillis;
    return util.formatElapsed(total);
}

function calculatePassed(tests) {
    var passed = util.filter(tests, function (test) {
        return test.status == 'PASS';
    });
    return passed.length;
}

function renderTestDetails(sortByStatus, target) {
    if (!window.elementsToRender.length)
        return;
    var tests = popUpTo(window.elementsToRender, 50);
    renderTestDetailsRows(tests, target);
    if (window.elementsToRender.length)
        setTimeout(function () {renderTestDetails(sortByStatus, target);}, 0);
    else
        configureTableSorter(sortByStatus);
}

function renderTestDetailsRows(tests, target) {
    var rows = $.tmpl('testDetailsTableRowTemplate', tests,
                      {logURL: window.settings.logURL});
    rows.find('a').click(stopPropagation);
    hideHiddenDetailsColumns(rows);
    rows.appendTo(target);
}

function configureTableSorter(sortByStatus) {
    var config = {headers: {3: {sorter: 'criticality'},
                            6: {sortInitialOrder: 'desc'},
                            7: {sorter: 'times'}},
                  selectorSort: '.details-col-header'};
    if (sortByStatus)
        config['sortList'] = [[4, 0], [3, 0]];
    $('#test-details').tablesorter(config);
}

function popUpTo(list, upTo) {
    var result = [];
    while (list.length > 0 && result.length < upTo)
        result.push(list.shift());
    return result;
}

function toggleDetailsColumn(name) {
    var column = $('.details-col-' + name);
    column.toggleClass('hidden');
    var hidden = column.hasClass('hidden');
    storage.set(name, hidden ? 'hidden' : 'visible');
    column.find('.details-col-toggle').html(hidden ? '&hellip;' : '&times;');
}

function hideHiddenDetailsColumns(elem) {
    var names = ['doc', 'tags', 'msg', 'elapsed', 'times'];
    for (var i = 0; i < names.length; i++) {
        var name = names[i];
        if (storage.get(name, 'visible') == 'hidden') {
            var column = elem.find('.details-col-' + name);
            column.addClass('hidden');
            column.find('.details-col-toggle').html('&hellip;');
        }
    }
}
</script>

<script type="text/x-jquery-tmpl" id="summaryTableTemplate">
  <h2>Summary Information</h2>
  <table class="details">
    <tr>
      <th>Status:</th>
      {{if criticalFailed}}
      <td><a href="#totals?critical" onclick="totalDetailSelected('critical')"
             class="fail">${criticalFailed} critical {{= testOrTask('{test}')}}{{if criticalFailed != 1}}s{{/if}} failed</a></td>
      {{else totalFailed}}
      <td><a href="#totals?critical" onclick="totalDetailSelected('critical')"
             class="pass">All critical {{= testOrTask('{test}')}}s passed</a></td>
      {{else}}
      <td><a href="#totals?all" onclick="totalDetailSelected('all')"
             class="pass">All {{= testOrTask('{test}')}}s passed</a></td>
      {{/if}}
    </tr>
    {{if doc()}}
    <tr>
      <th>Documentation:</th>
      <td class="doc">{{html doc()}}</td>
    </tr>
    {{/if}}
    {{each metadata}}
    <tr>
      <th>{{html $value[0]}}:</th>
      <td class="doc">{{html $value[1]}}</td>
    </tr>
    {{/each}}
    {{if times.startTime != 'N/A'}}
    <tr>
      <th>Start Time:</th>
      <td>${times.startTime}</td>
    </tr>
    {{/if}}
    {{if times.endTime != 'N/A'}}
    <tr>
      <th>End Time:</th>
      <td>${times.endTime}</td>
    </tr>
    {{/if}}
    <tr>
      <th>Elapsed Time:</th>
      <td>${times.elapsedTime}</td>
    </tr>
    {{if $item.logURL}}
    <tr>
      <th>Log File:</th>
      <td><a href="${$item.logURL}">${$item.logURL}</a></td>
    </tr>
    {{/if}}
  </table>
</script>

<script type="text/x-jquery-tmpl" id="totalStatisticsRowTemplate">
  <tr onclick="totalDetailSelected('${type}')" class="row-${$item.index}">
    <td class="stats-col-name">
      <div class="stat-name">
        <a href="#totals?${type}">{{html label}}</a>
      </div>
    </td>
    {{tmpl($data) 'statColumnsTemplate'}}
  </tr>
</script>

<script type="text/x-jquery-tmpl" id="tagStatisticsRowTemplate">
  <tr onclick="tagDetailSelected('${label}')" class="row-${$item.index}">
    <td class="stats-col-name" title="{{html doc}}">
      <div class="stat-name">
        <a href="#tags?${label}">{{html label}}</a>
        {{if info}}(${info}){{/if}}
      </div>
      <div class="tag-links">
        {{each links}}
        <span>[<a href="{{html $value.url}}" onclick="stopPropagation(event)"
                  title="{{html $value.url}}">{{html $value.title}}</a>]</span>
        {{/each}}
      </div>
    </td>
    {{tmpl($data) 'statColumnsTemplate'}}
  </tr>
</script>

<script type="text/x-jquery-tmpl" id="suiteStatisticsRowTemplate">
  <tr onclick="suiteDetailSelected('${id}')" class="row-${$item.index}">
    <td class="stats-col-name" title="{{html label}}">
      <div class="stat-name">
        <a href="#suites?${id}"><span class="parent-name">{{html formatParentName}}</span>{{html name}}</a>
      </div>
    </td>
    {{tmpl($data) 'statColumnsTemplate'}}
  </tr>
</script>

<script type="text/x-jquery-tmpl" id="detailsHeaderTemplate">
  <h2 id="${linkTarget}">{{= testOrTask('{Test}')}} Details</h2>
  <ul id="detail-tabs">
    <li class="${totalTabStatus} detail-tab">
      <a href="#totals" onclick="renderTotalSelector()">Totals</a>
    </li>
    <li class="${tagTabStatus} detail-tab">
      <a href="#tags" onclick="renderTagSelector()">Tags</a>
    </li>
    <li class="${suiteTabStatus} detail-tab">
      <a href="#suites" onclick="renderSuiteSelector()">Suites</a>
    </li>
    <li class="${searchTabStatus} detail-tab">
      <a href="#search" onclick="renderSearchSelector()">Search</a>
    </li>
  </ul>
</script>

<script  type="text/x-jquery-tmpl" id="totalDetailsSelectorTemplate">
  <table class="details" id="details-header">
    <tr class="selector">
      <th>Type:</th>
      <td id="normal-selector">
        <input id="radio-critical" type="radio" name="totals-radio"
               onclick="totalDetailSelected('critical')"
               {{if selected == 'critical'}}checked="checked"{{/if}}>
        <label for="radio-critical">Critical {{= testOrTask('{Test}')}}s</label><br>
        <input id="radio-all" type="radio" name="totals-radio"
               onclick="totalDetailSelected('all')"
               {{if selected == 'all'}}checked="checked"{{/if}}>
        <label for="radio-all">All {{= testOrTask('{Test}')}}s</label>
      </td>
      <td id="print-selector"></td>
    </tr>
  </table>
</script>

<script  type="text/x-jquery-tmpl" id="tagDetailsSelectorTemplate">
  <table class="details" id="details-header">
    <tr class="selector">
      <th>Name:</th>
      <td id="normal-selector">
        <select id="tag-detail-selector"
                onchange="tagDetailSelected(this.options[this.selectedIndex].value)">
          <option value="">Select tag...</option>
          {{each tags}}
          <option value="${$value.label}"
                  {{if $value.label == selected}}selected="selected"{{/if}}>
            {{html $value.label}} {{if $value.info}}(${$value.info}){{/if}}
          </option>
          {{/each}}
        </select>
      </td>
      <td id="print-selector"></td>
    </tr>
  </table>
</script>

<script  type="text/x-jquery-tmpl" id="suiteDetailsSelectorTemplate">
  <table class="details" id="details-header">
    <tr class="selector">
      <th>Name:</th>
      <td id="normal-selector">
        <select id="suite-detail-selector"
                onchange="suiteDetailSelected(this.options[this.selectedIndex].value)">
          <option value="">Select suite...</option>
          {{each suites}}
          <option value="${$value.id}"
                  {{if $value.id == selected}}selected="selected"{{/if}}>
            {{html $value.label}}
          </option>
          {{/each}}
        </select>
      </td>
      <td id="print-selector"></td>
    </tr>
  </table>
</script>

<script  type="text/x-jquery-tmpl" id="searchSelectorTemplate">
  <form action="javascript:void(0)">
    <table class="details" id="details-header">
      <tr class="selector first-selector">
        <th><label for="search-suite">Suite:</label></th>
        <td><input id="search-suite" type="text" value="${suite}"></td>
      </tr>
      <tr class="selector middle-selector">
        <th><label for="search-test">{{= testOrTask('{Test}')}}:</label></th>
        <td><input id="search-test" type="text" value="${test}"></td>
      </tr>
      <tr class="selector middle-selector">
        <th><label for="search-include">Include:</label></th>
        <td><input id="search-include" type="text" value="${include}"></td>
      </tr>
      <tr class="selector middle-selector">
        <th><label for="search-exclude">Exclude:</label></th>
        <td><input id="search-exclude" type="text" value="${exclude}"></td>
      </tr>
      <tr class="selector last-selector" id="search-buttons">
        <th></th>
        <td>
          <input type="submit" value="Search"
                 onclick="searchExecuted($('#search-suite').val(),
                                         $('#search-test').val(),
                                         $('#search-include').val(),
                                         $('#search-exclude').val());
                          return false;">
          <input type="button" value="Clear"
                 onclick="$('#search-suite').val('');
                          $('#search-test').val('');
                          $('#search-include').val('');
                          $('#search-exclude').val('');">
          <a href="javascript:void(0)" onclick="$('#search-help').toggle()"
             title="Toggle search help.">Help</a>
        </td>
      </tr>
      <tr id="search-help" style="display: none">
        <th></th>
        <td>
          <div>
            <h3>Search fields</h3>
            <p>
              {{= testOrTask('{Test}')}}s can be searched based on suite and
              {{= testOrTask('{test}')}} names as well as based on tags. If
              multiple search criteria are used, only
              {{= testOrTask('{test}')}}s matching all of them are included.
              Search fields have same semantics as
              <em>&#8209;&#8209;suite</em>,
              <em>&#8209;&#8209;{{= testOrTask('{test}')}}</em>,
              <em>&#8209;&#8209;include</em> and
              <em>&#8209;&#8209;exclude</em> command line options, respectively.
            </p>
            <table class="search-help-examples">
              <col class="help-item">
              <col class="help-explanation">
              <col class="help-examples>
              <tr>
                <th>Field</th>
                <th>Explanation</th>
                <th>Examples</th>
              </tr>
              <tr>
                <td>Suite</td>
                <td>
                  {{= testOrTask('{Test}')}}s in matching suites are included.
                  The pattern can match either suite's name or its full name
                  that contains also parent suite names.
                </td>
                <td>My Suite<br>Root.Parent.Sui*</td>
              </tr>
              <tr>
                <td>{{= testOrTask('{Test}')}}</td>
                <td>
                  Matching {{= testOrTask('{test}')}}s are included. The
                  pattern can match either {{= testOrTask('{test}')}}'s name
                  or its full name that contains also parent suite names.
                </td>
                <td>Example Name<br>Root.Pa*.T???</td>
              </tr>
              <tr>
                <td>Include</td>
                <td>
                  {{= testOrTask('{Test}')}}s that contain matching tags are
                  included.
                </td>
                <td>smoke<br>bug-*</td>
              </tr>
              <tr>
                <td>Exclude</td>
                <td>
                  {{= testOrTask('{Test}')}}s that contain matching tags are
                  not included.
                </td>
                <td>slow<br>feature-4?</td>
              </tr>
            </table>
            <h3>Patterns</h3>
            <p>
              All searches support <em>*</em> and <em>?</em> wildcards and are
              case, space and underscore insensitive. Tag related searches also
              support <em>AND</em>, <em>OR</em> and <em>NOT</em> (case-sensitive)
              combining operators. If operators are used together, their
              precedence, from highest to lowest, is <em>AND</em>, <em>OR</em>,
              <em>NOT</em>. See <em>Simple patterns</em> and
              <em>Tag patterns</em> sections in
              <a href="http://robotframework.org/robotframework/#user-guide">Robot
              Framework User Guide</a> for more details.
            </p>
            <table class="search-help-examples">
              <col class="help-item">
              <col class="help-explanation">
              <col class="help-examples>
              <tr>
                <th>Pattern</th>
                <th>Explanation</th>
                <th>Examples</th>
              </tr>
              <tr>
                <td>*</td>
                <td>Matches anything, even an empty string.</td>
                <td>f*<br>sprint-*</td>
              </tr>
              <tr>
                <td>?</td>
                <td>Matches any single character.</td>
                <td>f??<br>sprint-1?</td>
              </tr>
              <tr>
                <td>AND</td>
                <td>Matches if all patterns match.</td>
                <td>foo AND bar<br>x AND y* AND z??</td>
              </tr>
              <tr>
                <td>OR</td>
                <td>Matches if any pattern matches.</td>
                <td>foo OR bar<br>x OR y* OR z1 AND z2</td>
              </tr>
              <tr>
                <td>NOT</td>
                <td>
                    Matches if (optional) pattern before matches and pattern
                    after does not.
                </td>
                <td>foo NOT bar<br>* NOT id-* AND smoke<br>NOT bar</td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </form>
</script>

<script type="text/x-jquery-tmpl" id="tagOrTotalDetailsTemplate">
  <tr>
    <th>Status:</th>
    <td>${total} total, ${pass} passed, {{if fail}}<span class="fail">${fail} failed</span>{{else}}<span class="pass">0 failed</span>{{/if}}</td>
  </tr>
  {{if doc}}
  <tr>
    <th>Documentation:</th>
    <td>{{html doc}}</td>
  </tr>
  {{/if}}
  {{if combined}}
  <tr>
    <th>Pattern:</th>
    <td>{{html combined}}</td>
  </tr>
  {{/if}}
  {{if links}}{{if links.length}}
  <tr>
    <th>Links:</th>
    <td>{{each links}}<a href="{{html $value.url}}"
                         title="{{html $value.url}}">{{html $value.title}}</a> &nbsp; {{/each}}</td>
  </tr>
  {{/if}}{{/if}}
  <tr>
    <th>Total Time:</th>
    <td>${totalTime}</td>
  </tr>
</script>

<script type="text/x-jquery-tmpl" id="suiteDetailsTemplate">
  <tr>
    <th>Status:</th>
    <td>{{tmpl($data) 'suiteStatusMessageTemplate'}}</td>
  </tr>
  {{if doc()}}
  <tr>
    <th>Documentation:</th>
    <td class="doc">{{html doc()}}</td>
  </tr>
  {{/if}}
  {{each metadata}}
  <tr>
    <th>{{html $value[0]}}:</th>
    <td class="doc">{{html $value[1]}}</td>
  </tr>
  {{/each}}
  {{if message()}}
  <tr>
    <th>Message:</th>
    <td class="message">{{html message()}}</td>
  </tr>
  {{/if}}
  <tr>
    <th>Start / End Time:</th>
    <td>${times.startTime} / ${times.endTime}</td>
  </tr>
  <tr>
    <th>Elapsed Time:</th>
    <td>${times.elapsedTime}</td>
  </tr>
  {{if $item.logURL}}
  <tr>
    <th>Log File:</th>
    <td><a href="${$item.logURL}#${id}"
           title="{{html fullName}}">${$item.logURL}#${id}</a></td>
  </tr>
  {{/if}}
</script>

<script type="text/x-jquery-tmpl" id="testDetailsTableTemplate">
  <table id="test-details">
    <thead>
      <tr>
        <th class="details-col-name" title="Name">
          <div class='details-col-header'>Name</div>
        </th>
        <th class="details-col-doc" title="Documentation">
          <div class='details-col-toggle' onclick="toggleDetailsColumn('doc')">&times;</div>
          <div class='details-col-header'>Documentation</div>
        </th>
        <th class="details-col-tags" title="Tags">
          <div class='details-col-toggle' onclick="toggleDetailsColumn('tags')">&times;</div>
          <div class='details-col-header'>Tags</div>
        </th>
        <th class="details-col-crit" title="Critical">
          <div class='details-col-header'>Crit.</div>
        </th>
        <th class="details-col-status" title="Status">
          <div class='details-col-header'>Status</div>
        </th>
        <th class="details-col-msg" title="Message">
          <div class='details-col-toggle' onclick="toggleDetailsColumn('msg')">&times;</div>
          <div class='details-col-header'>Message</div>
        </th>
        <th class="details-col-elapsed" title="Elapsed Time">
          <div class='details-col-toggle' onclick="toggleDetailsColumn('elapsed')">&times;</div>
          <div class='details-col-header'>Elapsed</div>
        </th>
        <th class="details-col-times" title="Start Time / End Time">
          <div class='details-col-toggle' onclick="toggleDetailsColumn('times')">&times;</div>
          <div class='details-col-header'>Start / End</div>
        </th>
      </tr>
    </thead>
    <tbody></tbody>
  </table>
</script>

<script type="text/x-jquery-tmpl" id="testDetailsTableRowTemplate">
  {{if $item.logURL}}
  <tr onclick="location = '${$item.logURL}#${id}'" title="{{html fullName}}">
    <td class="details-col-name">
      <div><a href="${$item.logURL}#${id}"><span class="parent-name">{{html formatParentName}}</span>{{html name}}</a></div>
    </td>
  {{else}}
  <tr title="{{html fullName}}">
    <td class="details-col-name">
      <div><span class="parent-name">{{html formatParentName}}</span>{{html name}}</div>
    </td>
  {{/if}}
    <td class="details-col-doc"><div class="doc details-limited">{{html doc()}}</div></td>
    <td class="details-col-tags"><div>{{html tags.join(', ')}}</div></td>
    <td class="details-col-crit"><div>{{if isCritical}}yes{{else}}no{{/if}}</div></td>
    <td class="details-col-status"><div><span class="label ${status.toLowerCase()}">${status}</span></div></td>
    <td class="details-col-msg"><div class="message details-limited">{{html message()}}</div></td>
    <td class="details-col-elapsed"><div>${times.elapsedTime}</div></td>
    <td class="details-col-times"><div>${times.startTime}<br>${times.endTime}</div></td>
  </tr>
</script>

</body>
</html>
