<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东云服务器整机选型测试</title>
    <style>
        body {
            display: flex;
            font-family: Arial, sans-serif;
            margin: 0;
            height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: #f4f4f4;
            padding: 20px;
            border-right: 1px solid #ccc;
        }
        .content {
            flex-grow: 1;
            padding: 0;
            margin: 0;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin: 10px 0;
            cursor: pointer;
            padding: 5px;
            transition: background-color 0.3s;
        }
        .sidebar li:hover {
            background-color: #ddd;
        }
        .sidebar li.selected {
            background-color: #ccc;
            font-weight: bold;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <ul id="fileList">
            ###fileList###
        </ul>
    </div>
    <div class="content">
        <iframe id="contentFrame" src="" alt="文件内容"></iframe>
    </div>

    <script>
        // 默认加载第一个文件
        window.onload = function() {
            const firstItem = document.querySelector('.sidebar li');
            if (firstItem) {
                const firstFile = firstItem.getAttribute('onclick').match(/'([^']+)'/)[1];
                loadContent(firstFile, firstItem);
            }
        };

        function loadContent(file, element) {
            const frame = document.getElementById('contentFrame');
            frame.src = file;

            // 清除之前的选中状态
            const items = document.querySelectorAll('.sidebar li');
            items.forEach(item => {
                item.classList.remove('selected');
            });

            // 设置当前选中状态
            element.classList.add('selected');
        }
    </script>
</body>
</html>
