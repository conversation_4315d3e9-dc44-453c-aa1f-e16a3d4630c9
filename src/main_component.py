import os
import shutil
import xlsx2html
from datetime import datetime

from src.utils.delete_contents import delete_contents, delete_html_files
from src.utils.parse_log_testcase_info import get_log_testcase_info
from src.utils.write_excel import wirte_test_case_result_to_excel, write_attachments_files_to_excel
from src.utils.generate_index_html import generate_index_html


def generate_report_html(autologPath:str="./autologs_componet", isClearAutologs:bool=False):
    """ (1)日志文件到本地
        (2)分析日志文件并将测试结果写入测试表格
        (3)生成对应测试报告的html文件
        (4)汇聚所有测试报告html文件到索引html文件
    Args:
        autologPath (str, optional): 自动化日志路径. Defaults to "./autologs_componet".
        isClearAutologs (bool, optional): 是否清除自动化日志路径的文件. Defaults to True.
    """
    # 获取 sheet名 和 用例标识、自动化结果的索引
    sheetInfoList = [
        ("1.硬盘HDD", 4, 6),
        ("2.硬盘SATA-SSD", 4, 6),
        ("5.硬盘NVMe-SSD", 4, 6),
        # ("3.网卡", 4, 6),
        ("3.GPU卡", 4, 6),
        ("4.内存", 4, 6),
        ]
    if isClearAutologs:
        delete_contents(autologPath)
    else:
        delete_html_files(autologPath)
    logTestcasesInfo = get_log_testcase_info(autologPath)
    # 生成对应sheet页的html文件
    for sheetName, sacIdx, resultIdx in sheetInfoList:
        resultExcelFile = _generate_test_excel_file(sheetName)
        wirte_test_case_result_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx, resultIdx)
        write_attachments_files_to_excel(logTestcasesInfo, resultExcelFile, sheetName, sacIdx)
        htmlFile = resultExcelFile.replace("xlsx", "html")
        xlsx2html.xlsx2html(resultExcelFile, htmlFile, sheet=sheetName)
        os.remove(resultExcelFile)
        print(f"----- {htmlFile} 已经生成 -----")
    generate_index_html(autologPath)

def _generate_test_excel_file(sheetName):
    timestamp = datetime.now().strftime('%y%m%d%H%M%S')
    sourceFile = './src/templates/京东云服务器部件测试规范V3.2__SSP用例对应关系v0.3.xlsx'
    destinationFile = f'./autologs_componet/京东云服务器部件测试_{sheetName}-{timestamp}.xlsx'
    shutil.copy(sourceFile, destinationFile)
    return destinationFile


if __name__ == "__main__":
    generate_report_html()
    print("-----end-----")
