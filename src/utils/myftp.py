import os
from ctypes import *
import ftplib


class myFtp:

    ftp = ftplib.FTP()

    def __init__(self, host='************', port=21):
        self.ftp.connect(host, port)
        self.ftp.encoding = 'gbk'

    def Login(self, user='autotestci', passwd='autotestci'):
        self.ftp.login(user, passwd)
        print(self.ftp.welcome)

    def download_file(self, LocalFile, RemoteFile):  # 下载单个文件
        file_handler = open(LocalFile, 'wb')
        self.ftp.retrbinary('RETR ' + RemoteFile, file_handler.write)
        file_handler.close()
        return True

    def download_file_tree(self, LocalDir, RemoteDir, excludeDirs=None):  # 下载整个目录下的文件
        if excludeDirs is None:
            excludeDirs = []
        print("远程文件夹remoteDir:", RemoteDir)
        if not os.path.exists(LocalDir):
            os.makedirs(LocalDir)
        self.ftp.cwd(RemoteDir)
        RemoteNames = self.ftp.nlst()
        print("远程文件目录：", RemoteNames)
        for file in RemoteNames:
            Local = os.path.join(LocalDir, file)
            if file.find(".") == -1:  # 如果是目录
                if file not in excludeDirs:
                    if not os.path.exists(Local):
                        os.makedirs(Local)
                    self.download_file_tree(Local, file, excludeDirs)
                else:
                    print(f"跳过子文件夹 {file}")
            else:  # 如果是文件
                self.download_file(Local, file)
        self.ftp.cwd("..")
        return
    
    def close(self):
        self.ftp.quit()


if __name__ == "__main__":
    ftp = myFtp()
    ftp.Login()

    romte_path = '/report/auto/27584/893874'
    local_path = '/home/<USER>/Desktop/ftp/BMC'

    ftp.download_file_tree(local_path, romte_path)
    ftp.close()
    print("下载完成")
