
import re
import os
import openpyxl


TEST_NAME_SAC_IDX ={
    "1.BIOS定制化测试（EGS 2U）" : (6, 8),
    "1.BIOS定制化测试（EGS GPU）" : (6, 8),
    "1.BIOS定制化测试（Genoa）": (6, 8),
    "1.BIOS定制化测试（Whitley）": (6, 8),
    "1.BIOS定制化测试（Milan）": (6, 8),
    "1.BIOS定制化测试（HG4）": (6, 8),
    "2.BMC定制化测试": (4, 6),
    "3.Redfish测试": (7, 9),
    "4.稳定性测试": (4, 6),
    "5.性能测试": (4, 6),
    "6.FRU信息测试": (8, 10),
    "7.可运维测试": (6, 7)
}

STATISTICS_TEMPLATE =  '''<tr>
            <td>{testType}</td>
            <td>{totalCasesNum}</td>
            <td class="pass">{passCasesNum}</td>
            <td class="fail">{failCasesNum}</td>
            <td>{passRatio}%</td>
            </tr>'''

# VERSION_INFO_TEMPLATE = """<li class="info-item">
#         <span class="info-label">测试机型:</span>
#         <span class="info-value">{machineType}</span>
#     </li>
#     <li class="info-item">
#         <span class="info-label">BMC版本:</span>
#         <span class="info-value">{bmcVersion}</span>
#     </li>
#     <li class="info-item">
#         <span class="info-label">BIOS版本:</span>
#         <span class="info-value">{biosVersion}</span>
#     </li>
#     <li class="info-item">
#         <span class="info-label">报告生成时间:</span>
#         <span class="info-value">{generateTime}</span>
#     </li>"""

VERSION_INFO_TEMPLATE = """
            <p> <span class="info">测试机型: {machineType}</span>
                <span class="info">BMC版本: {bmcVersion}</span>
                <span class="info">BIOS版本: {biosVersion}</span>
                <span class="info">报告生成时间: {generateTime}</span>
            </p>
"""


def generate_statistics_html(logDir='./autologs', outputFilePath="./autologs/statistics.html"):
    contents = generate_content(logDir)
    replace_html_content(contents, outputFilePath)
    print("------ statistics.html 生成完成 ------")

def get_xlsx_file_names(dir="./autologs"):
    htmlFiles = []
    pattern = re.compile(r'_(\d+\..*)-\d+\.xlsx$')
    for filename in os.listdir(dir):
        if filename.endswith('.xlsx'):
            match = pattern.search(filename)
            if match:
                matchContent = match.group(1)
                htmlFiles.append((matchContent, os.path.join(dir, filename)))
    sortedFiles = sorted(htmlFiles, key=lambda x: float(x[0].split('.')[0]))
    return sortedFiles

def generate_content(logDir):
    xlsxFiles = get_xlsx_file_names(logDir)
    contents = []
    alltotalCasesNum = 0
    allpassCasesNum = 0
    allfailCasesNum = 0
    for sheetName, xlsxFile in xlsxFiles:
        sacIdx, resultIdx = TEST_NAME_SAC_IDX[sheetName]
        workbook = openpyxl.load_workbook(xlsxFile)
        sheet = workbook[sheetName]
        passCases = []
        failedCases = []
        noExcuteCases = []
        for row in range(3, sheet.max_row + 1):
            if sheet.cell(row=row, column=sacIdx).value and re.search("^SAC", sheet.cell(row=row, column=sacIdx).value):
                if sheet.cell(row=row, column=resultIdx).value == "PASS" and sheet.cell(row=row, column=sacIdx).value not in passCases:
                    passCases.append(sheet.cell(row=row, column=sacIdx).value)
                elif sheet.cell(row=row, column=resultIdx).value == "FAIL" and sheet.cell(row=row, column=sacIdx).value not in failedCases:
                    failedCases.append(sheet.cell(row=row, column=sacIdx).value)
                else:
                    noExcuteCases.append(sheet.cell(row=row, column=sacIdx).value)
        totalCasesNum = len(passCases + failedCases)
        passRatio = round(len(passCases) / totalCasesNum * 100, 1) if totalCasesNum != 0 else 0
        content = STATISTICS_TEMPLATE.format(testType = sheetName,
                                             totalCasesNum = totalCasesNum,
                                             passCasesNum = len(passCases),
                                             failCasesNum = len(failedCases),
                                             passRatio = passRatio
                                             )
        contents.append(content)
        alltotalCasesNum += totalCasesNum
        allpassCasesNum += len(passCases)
        allfailCasesNum += len(failedCases)
    allpassRatio = round(allpassCasesNum / alltotalCasesNum * 100, 1) if alltotalCasesNum != 0 else 0
    content = STATISTICS_TEMPLATE.format(testType = "总计",
                                        totalCasesNum = alltotalCasesNum,
                                        passCasesNum = allpassCasesNum,
                                        failCasesNum = allfailCasesNum,
                                        passRatio = allpassRatio
                                        )
    contents.append(content)
    print(contents)
    return "\n".join(contents)

def replace_html_content(newContent, outputFilePath="./autologs/statistics.html", inputFilePath='./src/templates/statistics_template.html'):
    versionContent = get_version_info_content()
    with open(inputFilePath, 'r', encoding='utf-8') as file:
        html_content = file.read()
    updated_content = re.sub(r'###statisticsData###', newContent, html_content)
    updated_content = re.sub(r'###changeListData###', versionContent, updated_content)
    with open(outputFilePath, 'w', encoding='utf-8') as file:
        file.write(updated_content)

def get_version_info_content():
    from datetime import datetime
    from src.versionInfo import VERSION_INFO
    content = VERSION_INFO_TEMPLATE.format(
                                        machineType = VERSION_INFO["machineType"],
                                        bmcVersion = VERSION_INFO["bmcVersion"],
                                        biosVersion = VERSION_INFO["biosVersion"],
                                        generateTime = datetime.now().strftime("%Y年%m月%d日"),
                                        )
    return content


if __name__ == "__main__":
    generate_statistics_html('/home/<USER>/Desktop/generate_report/autologs')