import os
import shutil
import subprocess
from src.utils.parse_log_testcase_info import get_log_testcase_info


def generate_all_pass_log(autologPath):
    logTestcasesInfo = get_log_testcase_info(autologPath)
    for logPath, caseInfo in logTestcasesInfo:
        passTestcases = []
        failedTestcases = []
        for sacId, testcase in caseInfo.items():
            if testcase[0] == "PASS":
                passTestcases.append(sacId)
            else:
                failedTestcases.append(sacId)
        outputXmlPath = os.path.join(os.path.dirname(logPath), "output.xml")
        if not failedTestcases:
            subprocess.run(['rebot', '-d', os.path.dirname(logPath), "-o", outputXmlPath, '-L', 'TRACE:INFO', outputXmlPath])
        else:
            if not passTestcases:
                testsaceDir = os.path.join("/home/<USER>/Desktop/generate_report/autologs", os.path.dirname(logPath).split("/")[-1])
                shutil.rmtree(testsaceDir)
                print(f"------已经删除 {testsaceDir}")
            else:
                cases = ['-t', *sum([[caseId+"*", '-t'] for caseId in passTestcases], [])][:-1]
                subprocess.run(['rebot', '-d', os.path.dirname(logPath), "-o", outputXmlPath, '-L', 'TRACE:INFO'] + cases + [outputXmlPath])


if __name__ == "__main__":
    # generate_all_pass_log("/home/<USER>/Desktop/generate_report/autologs")
    # generate_all_pass_log("/home/<USER>/Desktop/generate_report/autologs")
    generate_all_pass_log("/home/<USER>/Desktop/leting data/generate_report/autologs")
    print("----- end ------")
