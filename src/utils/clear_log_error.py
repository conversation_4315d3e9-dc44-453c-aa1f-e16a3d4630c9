import os
import subprocess
import xml.etree.ElementTree as ET

 
def clear_statistics_and_errors(filePath):
    tree = ET.parse(filePath)
    root = tree.getroot()
    for statistics in root.iter('statistics'):
        statistics.clear()
    for statistics in root.iter('errors'):
        statistics.clear()
    tree.write(filePath, encoding="utf-8", xml_declaration=True)


def clear_statistics_and_errors_by_folder(folderPath, isGenerateLog=False):
    for root, _, files in os.walk(folderPath):
        if root.count(os.sep) == folderPath.count(os.sep) + 1:
            hasOutput = 'output.xml' in files
            hasLog = 'log.html' in files
            hasReport = 'report.html' in files
            if hasOutput and hasLog and hasReport:
                outputPath = os.path.join(root, 'output.xml')
                print(outputPath)
                clear_statistics_and_errors(outputPath)
                if isGenerateLog:
                    subprocess.run(['rebot', '-d', root, outputPath])


if __name__ == "__main__":
    # clear_statistics_and_errors_by_folder("/home/<USER>/Desktop/generate_report/autologs", False)
    clear_statistics_and_errors_by_folder("/home/<USER>/Desktop/leting data/generate_report/autologs", False)
    # clear_statistics_and_errors_by_folder("/home/<USER>/Desktop/5300G5", False)
